'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { Section, SectionTitle } from '@/components/ui/Section';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import PlaceholderImage from '@/components/ui/PlaceholderImage';
import { mockColleges } from '@/data/colleges';
import { formatCurrency, generateStarRating, getInitials } from '@/utils/cn';

type TabType = 'overview' | 'academics' | 'hostels' | 'fests' | 'clubs' | 'reviews';

const CollegeDetailPage: React.FC = () => {
  const params = useParams();
  const collegeId = params.id as string;
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  const college = mockColleges.find(c => c.id === collegeId);

  if (!college) {
    return (
      <div className="min-h-screen pt-20 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🏫</div>
          <h1 className="text-3xl font-bold text-gray-700 mb-2">College Not Found</h1>
          <p className="text-gray-600 mb-6">The college you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          <Button variant="primary" onClick={() => window.history.back()}>
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview' },
    { id: 'academics', label: 'Academics' },
    { id: 'hostels', label: 'Hostels' },
    { id: 'fests', label: 'Fests' },
    { id: 'clubs', label: 'Clubs' },
    { id: 'reviews', label: 'Reviews' }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <Section background="white" padding="lg">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <PlaceholderImage
              text={`${college.name} Campus`}
              aspectRatio="16/9"
              color="brown"
              className="rounded-lg"
            />
          </div>
          
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold text-[var(--color-black)]">
                  {college.name}
                </h1>
                {college.nirfRanking && (
                  <div className="bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold">
                    NIRF #{college.nirfRanking}
                  </div>
                )}
              </div>
              <p className="text-lg text-gray-600 mb-4">
                {college.location.city}, {college.location.state}
              </p>
              <p className="text-gray-700 leading-relaxed">
                {college.description}
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                  {college.establishedYear}
                </div>
                <div className="text-sm text-gray-600">Established</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                  {college.type}
                </div>
                <div className="text-sm text-gray-600">Type</div>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-[var(--color-accent-brown)]/10 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="text-yellow-500 text-xl">
                  {generateStarRating(college.rating.overall)}
                </span>
                <span className="text-lg font-bold text-[var(--color-accent-brown)]">
                  {college.rating.overall}
                </span>
              </div>
              <span className="text-[var(--color-accent-brown)] font-medium">
                {college.reviews.length} reviews
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Counseling Category:</span>
                <span className="font-medium text-[var(--color-accent-brown)]">
                  {college.counselingCategory}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Affiliation:</span>
                <span className="font-medium">{college.affiliation}</span>
              </div>
            </div>

            <div className="flex gap-3">
              <Button variant="primary" size="md" className="flex-1">
                Apply Now
              </Button>
              <Button variant="secondary" size="md" className="flex-1">
                Compare
              </Button>
            </div>
          </div>
        </div>
      </Section>

      {/* Navigation Tabs */}
      <Section background="light" padding="sm">
        <div className="flex flex-wrap gap-2 justify-center">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "primary" : "outline"}
              size="sm"
              onClick={() => setActiveTab(tab.id as TabType)}
            >
              {tab.label}
            </Button>
          ))}
        </div>
      </Section>

      {/* Tab Content */}
      <Section background="white" padding="lg">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="text-center">
                <CardContent>
                  <div className="text-3xl font-bold text-[var(--color-accent-brown)] mb-2">
                    {college.academics.placementStats.placementPercentage}%
                  </div>
                  <div className="text-gray-600">Placement Rate</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent>
                  <div className="text-3xl font-bold text-[var(--color-accent-brown)] mb-2">
                    {formatCurrency(college.academics.placementStats.averagePackage)}
                  </div>
                  <div className="text-gray-600">Average Package</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent>
                  <div className="text-3xl font-bold text-[var(--color-accent-brown)] mb-2">
                    {formatCurrency(college.academics.placementStats.highestPackage)}
                  </div>
                  <div className="text-gray-600">Highest Package</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent>
                  <div className="text-3xl font-bold text-[var(--color-accent-brown)] mb-2">
                    {college.academics.departments.length}
                  </div>
                  <div className="text-gray-600">Departments</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Top Recruiters</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-3">
                  {college.academics.placementStats.topRecruiters.map((recruiter, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] rounded-full text-sm font-medium"
                    >
                      {recruiter}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Accreditations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-3">
                  {college.academics.accreditations.map((accreditation, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium"
                    >
                      {accreditation}
                    </span>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'academics' && (
          <div className="space-y-8">
            <SectionTitle size="md">Academic Departments</SectionTitle>
            <div className="grid gap-6">
              {college.academics.departments.map((dept) => (
                <Card key={dept.id}>
                  <CardHeader>
                    <CardTitle>{dept.name} ({dept.code})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 mb-4">{dept.syllabus}</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold mb-2">Faculty</h4>
                        <div className="space-y-2">
                          {dept.faculty.map((faculty) => (
                            <div key={faculty.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                              <div className="w-10 h-10 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold">
                                {getInitials(faculty.name)}
                              </div>
                              <div>
                                <div className="font-medium">{faculty.name}</div>
                                <div className="text-sm text-gray-600">{faculty.designation}</div>
                                <div className="text-sm text-gray-600">{faculty.specialization}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Laboratories</h4>
                        <div className="space-y-2">
                          {dept.labs.map((lab) => (
                            <div key={lab.id} className="p-3 bg-gray-50 rounded-lg">
                              <div className="font-medium">{lab.name}</div>
                              <div className="text-sm text-gray-600">Capacity: {lab.capacity}</div>
                              <div className="text-sm text-gray-600">{lab.description}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'hostels' && (
          <div className="space-y-8">
            <SectionTitle size="md">Hostel Facilities</SectionTitle>
            <div className="grid gap-6">
              {college.hostels.map((hostel) => (
                <Card key={hostel.id}>
                  <CardHeader>
                    <CardTitle>{hostel.name} ({hostel.type})</CardTitle>
                    <p className="text-gray-600">Capacity: {hostel.capacity} students</p>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <div>
                        <h4 className="font-semibold mb-3">Room Types</h4>
                        <div className="space-y-2">
                          {hostel.rooms.map((room, index) => (
                            <div key={index} className="p-3 bg-gray-50 rounded-lg">
                              <div className="flex justify-between items-center mb-2">
                                <span className="font-medium">{room.type}</span>
                                <span className="text-[var(--color-accent-brown)] font-bold">
                                  ₹{room.rent}/month
                                </span>
                              </div>
                              <div className="text-sm text-gray-600">
                                {room.facilities.join(', ')}
                              </div>
                              <div className={`text-xs mt-1 ${room.availability ? 'text-green-600' : 'text-red-600'}`}>
                                {room.availability ? 'Available' : 'Not Available'}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-3">Mess Information</h4>
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <div className="mb-2">
                            <span className="font-medium">Cost: </span>
                            <span className="text-[var(--color-accent-brown)] font-bold">
                              ₹{hostel.mess.cost}/month
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 space-y-1">
                            <div>Breakfast: {hostel.mess.timings.breakfast}</div>
                            <div>Lunch: {hostel.mess.timings.lunch}</div>
                            <div>Dinner: {hostel.mess.timings.dinner}</div>
                          </div>
                        </div>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-3">Facilities</h4>
                        <div className="flex flex-wrap gap-2">
                          {hostel.facilities.map((facility, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                            >
                              {facility}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'fests' && (
          <div className="space-y-8">
            <SectionTitle size="md">College Festivals</SectionTitle>
            <div className="grid gap-6">
              {college.fests.map((fest) => (
                <Card key={fest.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{fest.name}</CardTitle>
                        <p className="text-gray-600">{fest.type} Festival • {fest.duration} days</p>
                        <p className="text-sm text-gray-600">{fest.dates}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-[var(--color-accent-brown)]">
                          ₹{(fest.budget / 100000).toFixed(1)}L
                        </div>
                        <div className="text-sm text-gray-600">Budget</div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{fest.description}</p>
                    
                    {fest.celebrities && fest.celebrities.length > 0 && (
                      <div className="mb-4">
                        <h4 className="font-semibold mb-2">Celebrity Guests</h4>
                        <div className="flex flex-wrap gap-2">
                          {fest.celebrities.map((celebrity, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm"
                            >
                              {celebrity}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <h4 className="font-semibold mb-2">Events</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {fest.events.map((event, index) => (
                          <div key={index} className="p-3 bg-gray-50 rounded-lg">
                            <div className="font-medium">{event.name}</div>
                            <div className="text-sm text-gray-600 mb-2">{event.description}</div>
                            <div className="text-sm">
                              <span className="text-gray-600">Prizes: </span>
                              <span className="text-[var(--color-accent-brown)] font-medium">
                                ₹{event.prizes.join(', ₹')}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'clubs' && (
          <div className="space-y-8">
            <SectionTitle size="md">Student Clubs</SectionTitle>
            <div className="grid gap-6">
              {college.clubs.map((club) => (
                <Card key={club.id}>
                  <CardHeader>
                    <CardTitle>{club.name}</CardTitle>
                    <p className="text-gray-600">{club.category} Club</p>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{club.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold mb-2">Activities</h4>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {club.activities.map((activity, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                            >
                              {activity}
                            </span>
                          ))}
                        </div>
                        
                        <h4 className="font-semibold mb-2">Achievements</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          {club.achievements.map((achievement, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-[var(--color-accent-brown)] mt-1">•</span>
                              {achievement}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2">Leadership</h4>
                        <div className="space-y-2 mb-4">
                          {club.leadership.map((leader, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center text-sm font-bold">
                                {getInitials(leader.name)}
                              </div>
                              <div>
                                <div className="font-medium text-sm">{leader.name}</div>
                                <div className="text-xs text-gray-600">{leader.position}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                        
                        <div className="text-sm text-gray-600">
                          <div><strong>Membership:</strong> {club.membershipProcess}</div>
                          <div><strong>Meetings:</strong> {club.meetingFrequency}</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'reviews' && (
          <div className="space-y-8">
            <SectionTitle size="md">Student Reviews</SectionTitle>
            
            {/* Rating Summary */}
            <Card>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                      {college.rating.overall}
                    </div>
                    <div className="text-sm text-gray-600">Overall</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                      {college.rating.academics}
                    </div>
                    <div className="text-sm text-gray-600">Academics</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                      {college.rating.infrastructure}
                    </div>
                    <div className="text-sm text-gray-600">Infrastructure</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                      {college.rating.placements}
                    </div>
                    <div className="text-sm text-gray-600">Placements</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                      {college.rating.culture}
                    </div>
                    <div className="text-sm text-gray-600">Culture</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Reviews */}
            <div className="space-y-4">
              {college.reviews.map((review) => (
                <Card key={review.id}>
                  <CardContent>
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold">
                        {getInitials(review.userName)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium">{review.userName}</span>
                          {review.isVerified && (
                            <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                              Verified
                            </span>
                          )}
                          <span className="text-sm text-gray-500">{review.date}</span>
                        </div>
                        
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-yellow-500">
                            {generateStarRating(review.rating.overall)}
                          </span>
                          <span className="font-medium">{review.rating.overall}</span>
                        </div>
                        
                        <p className="text-gray-700 mb-3">{review.comment}</p>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <button className="flex items-center gap-1 hover:text-[var(--color-accent-brown)]">
                            👍 {review.likes}
                          </button>
                          <button className="flex items-center gap-1 hover:text-[var(--color-accent-brown)]">
                            👎 {review.dislikes}
                          </button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </Section>
    </div>
  );
};

export default CollegeDetailPage;
