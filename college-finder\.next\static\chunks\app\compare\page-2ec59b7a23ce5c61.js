(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[402],{8564:(e,a,l)=>{"use strict";l.r(a),l.d(a,{default:()=>m});var s=l(5155),t=l(2115),r=l(1514),c=l(7703),n=l(3741),i=l(9671),o=l(4028),d=l(1380);let m=()=>{let[e,a]=(0,t.useState)([]),[l,m]=(0,t.useState)(""),g=o.$.filter(e=>e.name.toLowerCase().includes(l.toLowerCase())||e.location.city.toLowerCase().includes(l.toLowerCase())||e.location.state.toLowerCase().includes(l.toLowerCase())),x=l=>{a(e.filter(e=>e.id!==l))},h=[{category:"Basic Information",items:[{label:"Established Year",getValue:e=>e.establishedYear},{label:"Type",getValue:e=>e.type},{label:"Location",getValue:e=>"".concat(e.location.city,", ").concat(e.location.state)},{label:"NIRF Ranking",getValue:e=>e.nirfRanking?"#".concat(e.nirfRanking):"Not Ranked"},{label:"Counseling Category",getValue:e=>e.counselingCategory}]},{category:"Academics & Placements",items:[{label:"Departments",getValue:e=>e.academics.departments.length},{label:"Placement Rate",getValue:e=>"".concat(e.academics.placementStats.placementPercentage,"%")},{label:"Average Package",getValue:e=>(0,d.vv)(e.academics.placementStats.averagePackage)},{label:"Highest Package",getValue:e=>(0,d.vv)(e.academics.placementStats.highestPackage)},{label:"Research Papers",getValue:e=>e.academics.researchPapers}]},{category:"Ratings",items:[{label:"Overall Rating",getValue:e=>"".concat(e.rating.overall," ").concat((0,d.XK)(e.rating.overall))},{label:"Academic Rating",getValue:e=>"".concat(e.rating.academics," ").concat((0,d.XK)(e.rating.academics))},{label:"Infrastructure Rating",getValue:e=>"".concat(e.rating.infrastructure," ").concat((0,d.XK)(e.rating.infrastructure))},{label:"Placement Rating",getValue:e=>"".concat(e.rating.placements," ").concat((0,d.XK)(e.rating.placements))},{label:"Culture Rating",getValue:e=>"".concat(e.rating.culture," ").concat((0,d.XK)(e.rating.culture))}]},{category:"Campus Life",items:[{label:"Hostels",getValue:e=>e.hostels.length},{label:"Festivals",getValue:e=>e.fests.length},{label:"Clubs",getValue:e=>e.clubs.length},{label:"Reviews",getValue:e=>e.reviews.length}]}];return(0,s.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,s.jsxs)(r.wn,{background:"light",padding:"lg",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(r._x,{size:"lg",className:"mb-4",children:"Compare Engineering Colleges"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Compare up to 3 colleges side-by-side to make an informed decision about your engineering education."})]}),e.length>0&&(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Selected Colleges (",e.length,"/3)"]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-4",children:e.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] px-4 py-2 rounded-full",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name}),(0,s.jsx)("button",{onClick:()=>x(e.id),className:"hover:bg-[var(--color-accent-brown)]/20 rounded-full p-1",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})]},e.id))})]}),(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsx)("input",{type:"text",placeholder:"Search colleges to add for comparison...",value:l,onChange:e=>m(e.target.value),className:"w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"})})]}),l&&(0,s.jsxs)(r.wn,{background:"white",padding:"md",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Search Results"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:g.slice(0,6).map(l=>{let t=e.find(e=>e.id===l.id),r=e.length<3&&!t;return(0,s.jsx)(c.Zp,{className:t?"border-[var(--color-accent-brown)] bg-[var(--color-accent-brown)]/5":"",children:(0,s.jsxs)(c.Wu,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,s.jsx)(i.A,{text:l.name.substring(0,2),width:50,height:50,className:"rounded-lg",color:"brown"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-semibold text-sm",children:l.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-600",children:[l.location.city,", ",l.location.state]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{className:"text-yellow-500 text-sm",children:(0,d.XK)(l.rating.overall)}),(0,s.jsx)("span",{className:"text-sm font-medium",children:l.rating.overall})]}),t?(0,s.jsx)(n.A,{variant:"outline",size:"sm",onClick:()=>x(l.id),children:"Remove"}):(0,s.jsx)(n.A,{variant:r?"primary":"outline",size:"sm",disabled:!r,onClick:()=>(l=>{e.length<3&&!e.find(e=>e.id===l.id)&&a([...e,l])})(l),children:r?"Add":"Max 3"})]})]})},l.id)})})]}),e.length>=2&&(0,s.jsxs)(r.wn,{background:"white",padding:"lg",children:[(0,s.jsx)(r._x,{size:"md",className:"mb-8",children:"College Comparison"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full border-collapse",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-left p-4 border-b-2 border-gray-200 w-64",children:(0,s.jsx)("div",{className:"font-semibold text-gray-700",children:"Comparison Criteria"})}),e.map(e=>(0,s.jsx)("th",{className:"text-center p-4 border-b-2 border-gray-200 min-w-64",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(i.A,{text:e.name,aspectRatio:"16/9",color:"brown",className:"rounded-lg"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-[var(--color-accent-brown)]",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[e.location.city,", ",e.location.state]})]})]})},e.id))]})}),(0,s.jsx)("tbody",{children:h.map(a=>(0,s.jsxs)(t.Fragment,{children:[(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:e.length+1,className:"p-4 bg-gray-50 border-b border-gray-200",children:(0,s.jsx)("div",{className:"font-semibold text-[var(--color-accent-brown)]",children:a.category})})}),a.items.map(a=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"p-4 border-b border-gray-100 font-medium text-gray-700",children:a.label}),e.map(e=>(0,s.jsx)("td",{className:"p-4 border-b border-gray-100 text-center",children:(0,s.jsx)("div",{className:"font-medium",children:a.getValue(e)})},e.id))]},a.label))]},a.category))})]})}),(0,s.jsxs)("div",{className:"flex justify-center gap-4 mt-8",children:[(0,s.jsx)(n.A,{variant:"primary",size:"lg",children:"Download Comparison"}),(0,s.jsx)(n.A,{variant:"secondary",size:"lg",children:"Share Comparison"})]})]}),0===e.length&&!l&&(0,s.jsx)(r.wn,{background:"white",padding:"lg",children:(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"⚖️"}),(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-700 mb-2",children:"Start Comparing Colleges"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Search and select up to 3 colleges to compare their features, ratings, and statistics side-by-side."}),(0,s.jsx)(n.A,{variant:"primary",onClick:()=>m("IIT"),children:"Search Colleges"})]})}),1===e.length&&(0,s.jsx)(r.wn,{background:"light",padding:"md",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Add at least one more college to start comparing"}),(0,s.jsx)(n.A,{variant:"outline",onClick:()=>m(""),children:"Search More Colleges"})]})})]})}},9709:(e,a,l)=>{Promise.resolve().then(l.bind(l,8564))}},e=>{e.O(0,[927,441,964,358],()=>e(e.s=9709)),_N_E=e.O()}]);