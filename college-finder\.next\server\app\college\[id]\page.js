(()=>{var a={};a.id=442,a.ids=[442],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3492:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(687),e=c(3210),f=c(6189),g=c(8078),h=c(8749),i=c(2643),j=c(4665),k=c(2182),l=c(2342);let m=()=>{let a=(0,f.useParams)().id,[b,c]=(0,e.useState)("overview"),m=k.$.find(b=>b.id===a);return m?(0,d.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,d.jsx)(g.wn,{background:"white",padding:"lg",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-2",children:(0,d.jsx)(j.A,{text:`${m.name} Campus`,aspectRatio:"16/9",color:"brown",className:"rounded-lg"})}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-[var(--color-black)]",children:m.name}),m.nirfRanking&&(0,d.jsxs)("div",{className:"bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold",children:["NIRF #",m.nirfRanking]})]}),(0,d.jsxs)("p",{className:"text-lg text-gray-600 mb-4",children:[m.location.city,", ",m.location.state]}),(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed",children:m.description})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.establishedYear}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Established"})]}),(0,d.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.type}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Type"})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-[var(--color-accent-brown)]/10 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-yellow-500 text-xl",children:(0,l.XK)(m.rating.overall)}),(0,d.jsx)("span",{className:"text-lg font-bold text-[var(--color-accent-brown)]",children:m.rating.overall})]}),(0,d.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-medium",children:[m.reviews.length," reviews"]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Counseling Category:"}),(0,d.jsx)("span",{className:"font-medium text-[var(--color-accent-brown)]",children:m.counselingCategory})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Affiliation:"}),(0,d.jsx)("span",{className:"font-medium",children:m.affiliation})]})]}),(0,d.jsxs)("div",{className:"flex gap-3",children:[(0,d.jsx)(i.A,{variant:"primary",size:"md",className:"flex-1",children:"Apply Now"}),(0,d.jsx)(i.A,{variant:"secondary",size:"md",className:"flex-1",children:"Compare"})]})]})]})}),(0,d.jsx)(g.wn,{background:"light",padding:"sm",children:(0,d.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:[{id:"overview",label:"Overview"},{id:"academics",label:"Academics"},{id:"hostels",label:"Hostels"},{id:"fests",label:"Fests"},{id:"clubs",label:"Clubs"},{id:"reviews",label:"Reviews"}].map(a=>(0,d.jsx)(i.A,{variant:b===a.id?"primary":"outline",size:"sm",onClick:()=>c(a.id),children:a.label},a.id))})}),(0,d.jsxs)(g.wn,{background:"white",padding:"lg",children:["overview"===b&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,d.jsx)(h.Zp,{className:"text-center",children:(0,d.jsxs)(h.Wu,{children:[(0,d.jsxs)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:[m.academics.placementStats.placementPercentage,"%"]}),(0,d.jsx)("div",{className:"text-gray-600",children:"Placement Rate"})]})}),(0,d.jsx)(h.Zp,{className:"text-center",children:(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:(0,l.vv)(m.academics.placementStats.averagePackage)}),(0,d.jsx)("div",{className:"text-gray-600",children:"Average Package"})]})}),(0,d.jsx)(h.Zp,{className:"text-center",children:(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:(0,l.vv)(m.academics.placementStats.highestPackage)}),(0,d.jsx)("div",{className:"text-gray-600",children:"Highest Package"})]})}),(0,d.jsx)(h.Zp,{className:"text-center",children:(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:m.academics.departments.length}),(0,d.jsx)("div",{className:"text-gray-600",children:"Departments"})]})})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsx)(h.aR,{children:(0,d.jsx)(h.ZB,{children:"Top Recruiters"})}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("div",{className:"flex flex-wrap gap-3",children:m.academics.placementStats.topRecruiters.map((a,b)=>(0,d.jsx)("span",{className:"px-4 py-2 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] rounded-full text-sm font-medium",children:a},b))})})]}),(0,d.jsxs)(h.Zp,{children:[(0,d.jsx)(h.aR,{children:(0,d.jsx)(h.ZB,{children:"Accreditations"})}),(0,d.jsx)(h.Wu,{children:(0,d.jsx)("div",{className:"flex flex-wrap gap-3",children:m.academics.accreditations.map((a,b)=>(0,d.jsx)("span",{className:"px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium",children:a},b))})})]})]}),"academics"===b&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)(g._x,{size:"md",children:"Academic Departments"}),(0,d.jsx)("div",{className:"grid gap-6",children:m.academics.departments.map(a=>(0,d.jsxs)(h.Zp,{children:[(0,d.jsx)(h.aR,{children:(0,d.jsxs)(h.ZB,{children:[a.name," (",a.code,")"]})}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:a.syllabus}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Faculty"}),(0,d.jsx)("div",{className:"space-y-2",children:a.faculty.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold",children:(0,l.IM)(a.name)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a.designation}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a.specialization})]})]},a.id))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Laboratories"}),(0,d.jsx)("div",{className:"space-y-2",children:a.labs.map(a=>(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["Capacity: ",a.capacity]}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a.description})]},a.id))})]})]})]})]},a.id))})]}),"hostels"===b&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)(g._x,{size:"md",children:"Hostel Facilities"}),(0,d.jsx)("div",{className:"grid gap-6",children:m.hostels.map(a=>(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsxs)(h.ZB,{children:[a.name," (",a.type,")"]}),(0,d.jsxs)("p",{className:"text-gray-600",children:["Capacity: ",a.capacity," students"]})]}),(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-3",children:"Room Types"}),(0,d.jsx)("div",{className:"space-y-2",children:a.rooms.map((a,b)=>(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,d.jsx)("span",{className:"font-medium",children:a.type}),(0,d.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-bold",children:["₹",a.rent,"/month"]})]}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a.facilities.join(", ")}),(0,d.jsx)("div",{className:`text-xs mt-1 ${a.availability?"text-green-600":"text-red-600"}`,children:a.availability?"Available":"Not Available"})]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-3",children:"Mess Information"}),(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"mb-2",children:[(0,d.jsx)("span",{className:"font-medium",children:"Cost: "}),(0,d.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-bold",children:["₹",a.mess.cost,"/month"]})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,d.jsxs)("div",{children:["Breakfast: ",a.mess.timings.breakfast]}),(0,d.jsxs)("div",{children:["Lunch: ",a.mess.timings.lunch]}),(0,d.jsxs)("div",{children:["Dinner: ",a.mess.timings.dinner]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-3",children:"Facilities"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.facilities.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:a},b))})]})]})})]},a.id))})]}),"fests"===b&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)(g._x,{size:"md",children:"College Festivals"}),(0,d.jsx)("div",{className:"grid gap-6",children:m.fests.map(a=>(0,d.jsxs)(h.Zp,{children:[(0,d.jsx)(h.aR,{children:(0,d.jsxs)("div",{className:"flex justify-between items-start",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(h.ZB,{children:a.name}),(0,d.jsxs)("p",{className:"text-gray-600",children:[a.type," Festival • ",a.duration," days"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.dates})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"text-lg font-bold text-[var(--color-accent-brown)]",children:["₹",(a.budget/1e5).toFixed(1),"L"]}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Budget"})]})]})}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("p",{className:"text-gray-700 mb-4",children:a.description}),a.celebrities&&a.celebrities.length>0&&(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Celebrity Guests"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:a.celebrities.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm",children:a},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Events"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:a.events.map((a,b)=>(0,d.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:a.description}),(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Prizes: "}),(0,d.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-medium",children:["₹",a.prizes.join(", ₹")]})]})]},b))})]})]})]},a.id))})]}),"clubs"===b&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)(g._x,{size:"md",children:"Student Clubs"}),(0,d.jsx)("div",{className:"grid gap-6",children:m.clubs.map(a=>(0,d.jsxs)(h.Zp,{children:[(0,d.jsxs)(h.aR,{children:[(0,d.jsx)(h.ZB,{children:a.name}),(0,d.jsxs)("p",{className:"text-gray-600",children:[a.category," Club"]})]}),(0,d.jsxs)(h.Wu,{children:[(0,d.jsx)("p",{className:"text-gray-700 mb-4",children:a.description}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Activities"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:a.activities.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:a},b))}),(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Achievements"}),(0,d.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:a.achievements.map((a,b)=>(0,d.jsxs)("li",{className:"flex items-start gap-2",children:[(0,d.jsx)("span",{className:"text-[var(--color-accent-brown)] mt-1",children:"•"}),a]},b))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-semibold mb-2",children:"Leadership"}),(0,d.jsx)("div",{className:"space-y-2 mb-4",children:a.leadership.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center text-sm font-bold",children:(0,l.IM)(a.name)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-sm",children:a.name}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:a.position})]})]},b))}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"Membership:"})," ",a.membershipProcess]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"Meetings:"})," ",a.meetingFrequency]})]})]})]})]})]},a.id))})]}),"reviews"===b&&(0,d.jsxs)("div",{className:"space-y-8",children:[(0,d.jsx)(g._x,{size:"md",children:"Student Reviews"}),(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.rating.overall}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Overall"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.rating.academics}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Academics"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.rating.infrastructure}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Infrastructure"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.rating.placements}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Placements"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:m.rating.culture}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:"Culture"})]})]})})}),(0,d.jsx)("div",{className:"space-y-4",children:m.reviews.map(a=>(0,d.jsx)(h.Zp,{children:(0,d.jsx)(h.Wu,{children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold",children:(0,l.IM)(a.userName)}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("span",{className:"font-medium",children:a.userName}),a.isVerified&&(0,d.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"Verified"}),(0,d.jsx)("span",{className:"text-sm text-gray-500",children:a.date})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,d.jsx)("span",{className:"text-yellow-500",children:(0,l.XK)(a.rating.overall)}),(0,d.jsx)("span",{className:"font-medium",children:a.rating.overall})]}),(0,d.jsx)("p",{className:"text-gray-700 mb-3",children:a.comment}),(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,d.jsxs)("button",{className:"flex items-center gap-1 hover:text-[var(--color-accent-brown)]",children:["\uD83D\uDC4D ",a.likes]}),(0,d.jsxs)("button",{className:"flex items-center gap-1 hover:text-[var(--color-accent-brown)]",children:["\uD83D\uDC4E ",a.dislikes]})]})]})]})})},a.id))})]})]})]}):(0,d.jsx)("div",{className:"min-h-screen pt-20 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFEB"}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-700 mb-2",children:"College Not Found"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"The college you're looking for doesn't exist or has been removed."}),(0,d.jsx)(i.A,{variant:"primary",onClick:()=>window.history.back(),children:"Go Back"})]})})}},3873:a=>{"use strict";a.exports=require("path")},5120:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["college",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,9726)),"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\college\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\college\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/college/[id]/page",pathname:"/college/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/college/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},9120:(a,b,c)=>{Promise.resolve().then(c.bind(c,3492))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9392:(a,b,c)=>{Promise.resolve().then(c.bind(c,9726))},9726:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\College Finder App\\\\college-finder\\\\src\\\\app\\\\college\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\college\\[id]\\page.tsx","default")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,408,251,566],()=>b(b.s=5120));module.exports=c})();