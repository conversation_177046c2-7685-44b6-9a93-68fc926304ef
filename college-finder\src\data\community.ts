import { Question, Answer, Poll, PollOption } from '@/types';

export const mockQuestions: Question[] = [
  {
    id: 'q1',
    userId: 'user-1',
    userName: '<PERSON><PERSON>',
    title: 'How is the placement scenario at IIT Delhi for CSE?',
    content: 'I am considering IIT Delhi for Computer Science. Can someone share insights about the placement opportunities, average packages, and top recruiting companies?',
    tags: ['IIT Delhi', 'CSE', 'Placements', 'Computer Science'],
    collegeId: 'iit-delhi',
    answers: [
      {
        id: 'a1',
        userId: 'user-2',
        userName: '<PERSON><PERSON>',
        content: 'IIT Delhi CSE has excellent placement records. The average package is around 18 LPA with top companies like Google, Microsoft, and Amazon regularly recruiting. The placement percentage is consistently above 95%.',
        votes: 15,
        date: '2024-01-16',
        isAccepted: true
      },
      {
        id: 'a2',
        userId: 'user-3',
        userName: 'Amit <PERSON>',
        content: 'Apart from the high packages, IIT Delhi also provides great research opportunities. Many students opt for higher studies or start their own companies. The alumni network is very strong.',
        votes: 8,
        date: '2024-01-17',
        isAccepted: false
      }
    ],
    votes: 23,
    views: 156,
    date: '2024-01-15',
    isResolved: true
  },
  {
    id: 'q2',
    userId: 'user-4',
    userName: 'Sneha Reddy',
    title: 'Hostel facilities at DTU - Need honest reviews',
    content: 'I got admission to DTU and need to know about hostel facilities. How are the rooms, mess food, and overall living conditions? Any specific hostel recommendations?',
    tags: ['DTU', 'Hostels', 'Accommodation', 'Delhi'],
    collegeId: 'dtu',
    answers: [
      {
        id: 'a3',
        userId: 'user-5',
        userName: 'Vikash Gupta',
        content: 'DTU hostels are decent but not luxurious. Boys hostels are better maintained than girls hostels. Mess food is average - you might want to explore nearby food options. Wi-Fi is good in most hostels.',
        votes: 12,
        date: '2024-02-02',
        isAccepted: true
      }
    ],
    votes: 18,
    views: 89,
    date: '2024-02-01',
    isResolved: true
  },
  {
    id: 'q3',
    userId: 'user-6',
    userName: 'Arjun Patel',
    title: 'RVCE vs PESIT - Which is better for CSE?',
    content: 'I have offers from both RVCE and PESIT for Computer Science. Can someone help me compare these colleges in terms of academics, placements, and campus life?',
    tags: ['RVCE', 'PESIT', 'CSE', 'Bangalore', 'Comparison'],
    collegeId: 'rvce-bangalore',
    answers: [
      {
        id: 'a4',
        userId: 'user-7',
        userName: 'Kavya Nair',
        content: 'Both are good colleges. RVCE has a slight edge in terms of brand value and alumni network. PESIT has better infrastructure and modern facilities. For CSE, both offer similar placement opportunities.',
        votes: 10,
        date: '2024-03-06',
        isAccepted: false
      }
    ],
    votes: 15,
    views: 67,
    date: '2024-03-05',
    isResolved: false
  },
  {
    id: 'q4',
    userId: 'user-8',
    userName: 'Ananya Das',
    title: 'Research opportunities at Jadavpur University',
    content: 'I am interested in pursuing research in Electronics. How are the research facilities and opportunities at JU? Are there good professors for guidance?',
    tags: ['Jadavpur University', 'Research', 'Electronics', 'PhD'],
    collegeId: 'jadavpur-university',
    answers: [
      {
        id: 'a5',
        userId: 'user-9',
        userName: 'Souvik Das',
        content: 'JU has excellent research facilities, especially in Electronics and Communication. Many professors are actively involved in research projects with ISRO and DRDO. The university encourages undergraduate research participation.',
        votes: 20,
        date: '2024-03-01',
        isAccepted: true
      }
    ],
    votes: 25,
    views: 134,
    date: '2024-02-28',
    isResolved: true
  }
];

export const mockPolls: Poll[] = [
  {
    id: 'p1',
    userId: 'user-10',
    userName: 'College Finder Team',
    title: 'Most Important Factor When Choosing a College',
    description: 'What do you consider the most important factor when selecting an engineering college?',
    options: [
      {
        id: 'o1',
        text: 'Placement Records',
        votes: 245
      },
      {
        id: 'o2',
        text: 'Academic Reputation',
        votes: 189
      },
      {
        id: 'o3',
        text: 'Infrastructure & Facilities',
        votes: 156
      },
      {
        id: 'o4',
        text: 'Location & Campus Life',
        votes: 98
      },
      {
        id: 'o5',
        text: 'Fee Structure',
        votes: 87
      }
    ],
    totalVotes: 775,
    expiryDate: '2024-12-31',
    tags: ['College Selection', 'Survey', 'Engineering'],
    date: '2024-01-01'
  },
  {
    id: 'p2',
    userId: 'user-11',
    userName: 'Tech Student',
    title: 'Best Programming Language for Beginners',
    description: 'Which programming language would you recommend for someone starting their coding journey in engineering?',
    options: [
      {
        id: 'o6',
        text: 'Python',
        votes: 312
      },
      {
        id: 'o7',
        text: 'Java',
        votes: 198
      },
      {
        id: 'o8',
        text: 'C++',
        votes: 145
      },
      {
        id: 'o9',
        text: 'JavaScript',
        votes: 89
      },
      {
        id: 'o10',
        text: 'C',
        votes: 76
      }
    ],
    totalVotes: 820,
    tags: ['Programming', 'Beginners', 'Technology'],
    date: '2024-02-15'
  },
  {
    id: 'p3',
    userId: 'user-12',
    userName: 'Campus Life Explorer',
    title: 'Most Exciting College Festival Type',
    description: 'What type of college festival do you enjoy the most?',
    options: [
      {
        id: 'o11',
        text: 'Technical Fest (Robotics, Coding, etc.)',
        votes: 167
      },
      {
        id: 'o12',
        text: 'Cultural Fest (Music, Dance, Drama)',
        votes: 234
      },
      {
        id: 'o13',
        text: 'Sports Fest',
        votes: 89
      },
      {
        id: 'o14',
        text: 'Literary Fest',
        votes: 45
      }
    ],
    totalVotes: 535,
    collegeId: 'general',
    tags: ['Festivals', 'Campus Life', 'Events'],
    date: '2024-03-01'
  }
];

// Helper functions for community features
export function getQuestionsByCollege(collegeId: string): Question[] {
  return mockQuestions.filter(q => q.collegeId === collegeId);
}

export function getQuestionsByTag(tag: string): Question[] {
  return mockQuestions.filter(q => q.tags.includes(tag));
}

export function getPopularQuestions(limit: number = 5): Question[] {
  return mockQuestions
    .sort((a, b) => b.votes - a.votes)
    .slice(0, limit);
}

export function getRecentQuestions(limit: number = 5): Question[] {
  return mockQuestions
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, limit);
}

export function getActivePolls(): Poll[] {
  const now = new Date();
  return mockPolls.filter(poll => 
    !poll.expiryDate || new Date(poll.expiryDate) > now
  );
}

export function getPollsByCollege(collegeId: string): Poll[] {
  return mockPolls.filter(poll => poll.collegeId === collegeId);
}
