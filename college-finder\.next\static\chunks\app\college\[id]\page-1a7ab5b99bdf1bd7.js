(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[442],{1871:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var l=a(5155),c=a(2115),i=a(5695),t=a(1514),r=a(7703),d=a(3741),n=a(9671),x=a(4028),m=a(1380);let o=()=>{let e=(0,i.useParams)().id,[s,a]=(0,c.useState)("overview"),o=x.$.find(s=>s.id===e);return o?(0,l.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,l.jsx)(t.wn,{background:"white",padding:"lg",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsx)("div",{className:"lg:col-span-2",children:(0,l.jsx)(n.A,{text:"".concat(o.name," Campus"),aspectRatio:"16/9",color:"brown",className:"rounded-lg"})}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-[var(--color-black)]",children:o.name}),o.nirfRanking&&(0,l.jsxs)("div",{className:"bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold",children:["NIRF #",o.nirfRanking]})]}),(0,l.jsxs)("p",{className:"text-lg text-gray-600 mb-4",children:[o.location.city,", ",o.location.state]}),(0,l.jsx)("p",{className:"text-gray-700 leading-relaxed",children:o.description})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.establishedYear}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Established"})]}),(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.type}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Type"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 bg-[var(--color-accent-brown)]/10 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"text-yellow-500 text-xl",children:(0,m.XK)(o.rating.overall)}),(0,l.jsx)("span",{className:"text-lg font-bold text-[var(--color-accent-brown)]",children:o.rating.overall})]}),(0,l.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-medium",children:[o.reviews.length," reviews"]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Counseling Category:"}),(0,l.jsx)("span",{className:"font-medium text-[var(--color-accent-brown)]",children:o.counselingCategory})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Affiliation:"}),(0,l.jsx)("span",{className:"font-medium",children:o.affiliation})]})]}),(0,l.jsxs)("div",{className:"flex gap-3",children:[(0,l.jsx)(d.A,{variant:"primary",size:"md",className:"flex-1",children:"Apply Now"}),(0,l.jsx)(d.A,{variant:"secondary",size:"md",className:"flex-1",children:"Compare"})]})]})]})}),(0,l.jsx)(t.wn,{background:"light",padding:"sm",children:(0,l.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:[{id:"overview",label:"Overview"},{id:"academics",label:"Academics"},{id:"hostels",label:"Hostels"},{id:"fests",label:"Fests"},{id:"clubs",label:"Clubs"},{id:"reviews",label:"Reviews"}].map(e=>(0,l.jsx)(d.A,{variant:s===e.id?"primary":"outline",size:"sm",onClick:()=>a(e.id),children:e.label},e.id))})}),(0,l.jsxs)(t.wn,{background:"white",padding:"lg",children:["overview"===s&&(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,l.jsx)(r.Zp,{className:"text-center",children:(0,l.jsxs)(r.Wu,{children:[(0,l.jsxs)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:[o.academics.placementStats.placementPercentage,"%"]}),(0,l.jsx)("div",{className:"text-gray-600",children:"Placement Rate"})]})}),(0,l.jsx)(r.Zp,{className:"text-center",children:(0,l.jsxs)(r.Wu,{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:(0,m.vv)(o.academics.placementStats.averagePackage)}),(0,l.jsx)("div",{className:"text-gray-600",children:"Average Package"})]})}),(0,l.jsx)(r.Zp,{className:"text-center",children:(0,l.jsxs)(r.Wu,{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:(0,m.vv)(o.academics.placementStats.highestPackage)}),(0,l.jsx)("div",{className:"text-gray-600",children:"Highest Package"})]})}),(0,l.jsx)(r.Zp,{className:"text-center",children:(0,l.jsxs)(r.Wu,{children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-[var(--color-accent-brown)] mb-2",children:o.academics.departments.length}),(0,l.jsx)("div",{className:"text-gray-600",children:"Departments"})]})})]}),(0,l.jsxs)(r.Zp,{children:[(0,l.jsx)(r.aR,{children:(0,l.jsx)(r.ZB,{children:"Top Recruiters"})}),(0,l.jsx)(r.Wu,{children:(0,l.jsx)("div",{className:"flex flex-wrap gap-3",children:o.academics.placementStats.topRecruiters.map((e,s)=>(0,l.jsx)("span",{className:"px-4 py-2 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] rounded-full text-sm font-medium",children:e},s))})})]}),(0,l.jsxs)(r.Zp,{children:[(0,l.jsx)(r.aR,{children:(0,l.jsx)(r.ZB,{children:"Accreditations"})}),(0,l.jsx)(r.Wu,{children:(0,l.jsx)("div",{className:"flex flex-wrap gap-3",children:o.academics.accreditations.map((e,s)=>(0,l.jsx)("span",{className:"px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium",children:e},s))})})]})]}),"academics"===s&&(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsx)(t._x,{size:"md",children:"Academic Departments"}),(0,l.jsx)("div",{className:"grid gap-6",children:o.academics.departments.map(e=>(0,l.jsxs)(r.Zp,{children:[(0,l.jsx)(r.aR,{children:(0,l.jsxs)(r.ZB,{children:[e.name," (",e.code,")"]})}),(0,l.jsxs)(r.Wu,{children:[(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:e.syllabus}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Faculty"}),(0,l.jsx)("div",{className:"space-y-2",children:e.faculty.map(e=>(0,l.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold",children:(0,m.IM)(e.name)}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:e.name}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:e.designation}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:e.specialization})]})]},e.id))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Laboratories"}),(0,l.jsx)("div",{className:"space-y-2",children:e.labs.map(e=>(0,l.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"font-medium",children:e.name}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:["Capacity: ",e.capacity]}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]},e.id))})]})]})]})]},e.id))})]}),"hostels"===s&&(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsx)(t._x,{size:"md",children:"Hostel Facilities"}),(0,l.jsx)("div",{className:"grid gap-6",children:o.hostels.map(e=>(0,l.jsxs)(r.Zp,{children:[(0,l.jsxs)(r.aR,{children:[(0,l.jsxs)(r.ZB,{children:[e.name," (",e.type,")"]}),(0,l.jsxs)("p",{className:"text-gray-600",children:["Capacity: ",e.capacity," students"]})]}),(0,l.jsx)(r.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"Room Types"}),(0,l.jsx)("div",{className:"space-y-2",children:e.rooms.map((e,s)=>(0,l.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,l.jsx)("span",{className:"font-medium",children:e.type}),(0,l.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-bold",children:["₹",e.rent,"/month"]})]}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:e.facilities.join(", ")}),(0,l.jsx)("div",{className:"text-xs mt-1 ".concat(e.availability?"text-green-600":"text-red-600"),children:e.availability?"Available":"Not Available"})]},s))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"Mess Information"}),(0,l.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"mb-2",children:[(0,l.jsx)("span",{className:"font-medium",children:"Cost: "}),(0,l.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-bold",children:["₹",e.mess.cost,"/month"]})]}),(0,l.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,l.jsxs)("div",{children:["Breakfast: ",e.mess.timings.breakfast]}),(0,l.jsxs)("div",{children:["Lunch: ",e.mess.timings.lunch]}),(0,l.jsxs)("div",{children:["Dinner: ",e.mess.timings.dinner]})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-3",children:"Facilities"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.facilities.map((e,s)=>(0,l.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:e},s))})]})]})})]},e.id))})]}),"fests"===s&&(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsx)(t._x,{size:"md",children:"College Festivals"}),(0,l.jsx)("div",{className:"grid gap-6",children:o.fests.map(e=>(0,l.jsxs)(r.Zp,{children:[(0,l.jsx)(r.aR,{children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(r.ZB,{children:e.name}),(0,l.jsxs)("p",{className:"text-gray-600",children:[e.type," Festival • ",e.duration," days"]}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:e.dates})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"text-lg font-bold text-[var(--color-accent-brown)]",children:["₹",(e.budget/1e5).toFixed(1),"L"]}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Budget"})]})]})}),(0,l.jsxs)(r.Wu,{children:[(0,l.jsx)("p",{className:"text-gray-700 mb-4",children:e.description}),e.celebrities&&e.celebrities.length>0&&(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Celebrity Guests"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.celebrities.map((e,s)=>(0,l.jsx)("span",{className:"px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm",children:e},s))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Events"}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:e.events.map((e,s)=>(0,l.jsxs)("div",{className:"p-3 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"font-medium",children:e.name}),(0,l.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,l.jsxs)("div",{className:"text-sm",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Prizes: "}),(0,l.jsxs)("span",{className:"text-[var(--color-accent-brown)] font-medium",children:["₹",e.prizes.join(", ₹")]})]})]},s))})]})]})]},e.id))})]}),"clubs"===s&&(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsx)(t._x,{size:"md",children:"Student Clubs"}),(0,l.jsx)("div",{className:"grid gap-6",children:o.clubs.map(e=>(0,l.jsxs)(r.Zp,{children:[(0,l.jsxs)(r.aR,{children:[(0,l.jsx)(r.ZB,{children:e.name}),(0,l.jsxs)("p",{className:"text-gray-600",children:[e.category," Club"]})]}),(0,l.jsxs)(r.Wu,{children:[(0,l.jsx)("p",{className:"text-gray-700 mb-4",children:e.description}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Activities"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:e.activities.map((e,s)=>(0,l.jsx)("span",{className:"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm",children:e},s))}),(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Achievements"}),(0,l.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.achievements.map((e,s)=>(0,l.jsxs)("li",{className:"flex items-start gap-2",children:[(0,l.jsx)("span",{className:"text-[var(--color-accent-brown)] mt-1",children:"•"}),e]},s))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-semibold mb-2",children:"Leadership"}),(0,l.jsx)("div",{className:"space-y-2 mb-4",children:e.leadership.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center text-sm font-bold",children:(0,m.IM)(e.name)}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium text-sm",children:e.name}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:e.position})]})]},s))}),(0,l.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"Membership:"})," ",e.membershipProcess]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"Meetings:"})," ",e.meetingFrequency]})]})]})]})]})]},e.id))})]}),"reviews"===s&&(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsx)(t._x,{size:"md",children:"Student Reviews"}),(0,l.jsx)(r.Zp,{children:(0,l.jsx)(r.Wu,{children:(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.rating.overall}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Overall"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.rating.academics}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Academics"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.rating.infrastructure}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Infrastructure"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.rating.placements}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Placements"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:o.rating.culture}),(0,l.jsx)("div",{className:"text-sm text-gray-600",children:"Culture"})]})]})})}),(0,l.jsx)("div",{className:"space-y-4",children:o.reviews.map(e=>(0,l.jsx)(r.Zp,{children:(0,l.jsx)(r.Wu,{children:(0,l.jsxs)("div",{className:"flex items-start gap-4",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold",children:(0,m.IM)(e.userName)}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,l.jsx)("span",{className:"font-medium",children:e.userName}),e.isVerified&&(0,l.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"Verified"}),(0,l.jsx)("span",{className:"text-sm text-gray-500",children:e.date})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,l.jsx)("span",{className:"text-yellow-500",children:(0,m.XK)(e.rating.overall)}),(0,l.jsx)("span",{className:"font-medium",children:e.rating.overall})]}),(0,l.jsx)("p",{className:"text-gray-700 mb-3",children:e.comment}),(0,l.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,l.jsxs)("button",{className:"flex items-center gap-1 hover:text-[var(--color-accent-brown)]",children:["\uD83D\uDC4D ",e.likes]}),(0,l.jsxs)("button",{className:"flex items-center gap-1 hover:text-[var(--color-accent-brown)]",children:["\uD83D\uDC4E ",e.dislikes]})]})]})]})})},e.id))})]})]})]}):(0,l.jsx)("div",{className:"min-h-screen pt-20 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFEB"}),(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-700 mb-2",children:"College Not Found"}),(0,l.jsx)("p",{className:"text-gray-600 mb-6",children:"The college you're looking for doesn't exist or has been removed."}),(0,l.jsx)(d.A,{variant:"primary",onClick:()=>window.history.back(),children:"Go Back"})]})})}},4680:(e,s,a)=>{Promise.resolve().then(a.bind(a,1871))},5695:(e,s,a)=>{"use strict";var l=a(8999);a.o(l,"useParams")&&a.d(s,{useParams:function(){return l.useParams}}),a.o(l,"usePathname")&&a.d(s,{usePathname:function(){return l.usePathname}}),a.o(l,"useRouter")&&a.d(s,{useRouter:function(){return l.useRouter}}),a.o(l,"useSearchParams")&&a.d(s,{useSearchParams:function(){return l.useSearchParams}})}},e=>{e.O(0,[927,441,964,358],()=>e(e.s=4680)),_N_E=e.O()}]);