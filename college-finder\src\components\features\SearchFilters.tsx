'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { SearchFilters as SearchFiltersType, CounselingCategory } from '@/types';
import { counselingCategories } from '@/data/colleges';

interface SearchFiltersProps {
  filters: SearchFiltersType;
  onFiltersChange: (filters: SearchFiltersType) => void;
  onClearFilters: () => void;
}

const SearchFilters: React.FC<SearchFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleCounselingCategoryChange = (category: CounselingCategory, checked: boolean) => {
    const current = filters.counselingCategory || [];
    const updated = checked
      ? [...current, category]
      : current.filter(c => c !== category);
    
    onFiltersChange({
      ...filters,
      counselingCategory: updated.length > 0 ? updated : undefined
    });
  };

  const handleTypeChange = (type: 'Government' | 'Private' | 'Deemed', checked: boolean) => {
    const current = filters.type || [];
    const updated = checked
      ? [...current, type]
      : current.filter(t => t !== type);
    
    onFiltersChange({
      ...filters,
      type: updated.length > 0 ? updated : undefined
    });
  };

  const handleStateChange = (state: string, checked: boolean) => {
    const current = filters.location?.states || [];
    const updated = checked
      ? [...current, state]
      : current.filter(s => s !== state);
    
    onFiltersChange({
      ...filters,
      location: {
        ...filters.location,
        states: updated.length > 0 ? updated : undefined
      }
    });
  };

  const handleNirfRankingChange = (field: 'min' | 'max', value: string) => {
    const numValue = value ? parseInt(value) : undefined;
    onFiltersChange({
      ...filters,
      nirfRanking: {
        ...filters.nirfRanking,
        [field]: numValue
      }
    });
  };

  const handleRatingChange = (field: 'min' | 'max', value: string) => {
    const numValue = value ? parseFloat(value) : undefined;
    onFiltersChange({
      ...filters,
      rating: {
        ...filters.rating,
        [field]: numValue
      }
    });
  };

  const states = [
    'Delhi', 'Karnataka', 'Maharashtra', 'West Bengal', 'Tamil Nadu',
    'Uttar Pradesh', 'Gujarat', 'Rajasthan', 'Andhra Pradesh', 'Telangana'
  ];

  const hasActiveFilters = !!(
    filters.counselingCategory?.length ||
    filters.type?.length ||
    filters.location?.states?.length ||
    filters.nirfRanking?.min ||
    filters.nirfRanking?.max ||
    filters.rating?.min ||
    filters.rating?.max
  );

  return (
    <Card className="sticky top-24">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Filters</CardTitle>
          <div className="flex gap-2">
            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
              >
                Clear All
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="md:hidden"
            >
              {isExpanded ? 'Hide' : 'Show'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={`space-y-6 ${!isExpanded ? 'hidden md:block' : ''}`}>
        {/* Counseling Category */}
        <div>
          <h3 className="font-semibold mb-3">Counseling Category</h3>
          <div className="space-y-2">
            {counselingCategories.map((category) => (
              <label key={category.name} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.counselingCategory?.includes(category.name) || false}
                  onChange={(e) => handleCounselingCategoryChange(category.name, e.target.checked)}
                  className="rounded border-gray-300 text-[var(--color-accent-brown)] focus:ring-[var(--color-accent-brown)]"
                />
                <span className="text-sm">{category.name}</span>
                <span className="text-xs text-gray-500">({category.collegeCount})</span>
              </label>
            ))}
          </div>
        </div>

        {/* College Type */}
        <div>
          <h3 className="font-semibold mb-3">College Type</h3>
          <div className="space-y-2">
            {['Government', 'Private', 'Deemed'].map((type) => (
              <label key={type} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.type?.includes(type as 'Government' | 'Private' | 'Deemed') || false}
                  onChange={(e) => handleTypeChange(type as 'Government' | 'Private' | 'Deemed', e.target.checked)}
                  className="rounded border-gray-300 text-[var(--color-accent-brown)] focus:ring-[var(--color-accent-brown)]"
                />
                <span className="text-sm">{type}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Location */}
        <div>
          <h3 className="font-semibold mb-3">State</h3>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {states.map((state) => (
              <label key={state} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.location?.states?.includes(state) || false}
                  onChange={(e) => handleStateChange(state, e.target.checked)}
                  className="rounded border-gray-300 text-[var(--color-accent-brown)] focus:ring-[var(--color-accent-brown)]"
                />
                <span className="text-sm">{state}</span>
              </label>
            ))}
          </div>
        </div>

        {/* NIRF Ranking */}
        <div>
          <h3 className="font-semibold mb-3">NIRF Ranking</h3>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-600 mb-1">Min Rank</label>
              <input
                type="number"
                placeholder="1"
                min="1"
                max="200"
                value={filters.nirfRanking?.min || ''}
                onChange={(e) => handleNirfRankingChange('min', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Max Rank</label>
              <input
                type="number"
                placeholder="200"
                min="1"
                max="200"
                value={filters.nirfRanking?.max || ''}
                onChange={(e) => handleNirfRankingChange('max', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Rating */}
        <div>
          <h3 className="font-semibold mb-3">Rating</h3>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-600 mb-1">Min Rating</label>
              <input
                type="number"
                placeholder="1.0"
                min="1"
                max="5"
                step="0.1"
                value={filters.rating?.min || ''}
                onChange={(e) => handleRatingChange('min', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Max Rating</label>
              <input
                type="number"
                placeholder="5.0"
                min="1"
                max="5"
                step="0.1"
                value={filters.rating?.max || ''}
                onChange={(e) => handleRatingChange('max', e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Quick Filters */}
        <div>
          <h3 className="font-semibold mb-3">Quick Filters</h3>
          <div className="space-y-2">
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onFiltersChange({
                ...filters,
                nirfRanking: { min: 1, max: 50 }
              })}
            >
              Top 50 NIRF Ranked
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onFiltersChange({
                ...filters,
                rating: { min: 4.0, max: 5.0 }
              })}
            >
              Highly Rated (4.0+)
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full justify-start"
              onClick={() => onFiltersChange({
                ...filters,
                type: ['Government']
              })}
            >
              Government Colleges
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchFilters;
