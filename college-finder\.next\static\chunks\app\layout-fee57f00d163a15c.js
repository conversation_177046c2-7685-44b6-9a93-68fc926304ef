(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{260:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,5430))},347:()=>{},1380:(e,t,r)=>{"use strict";function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}(t)}function o(e){return new Intl.NumberFormat("en-IN",{style:"currency",currency:"INR",maximumFractionDigits:0}).format(e)}function a(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/[\s_-]+/g,"-").replace(/^-+|-+$/g,"")}function s(e){return e.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)}function c(e){let t="string"==typeof e?new Date(e):e,r=Math.floor((new Date().getTime()-t.getTime())/1e3);return r<60?"just now":r<3600?"".concat(Math.floor(r/60)," minutes ago"):r<86400?"".concat(Math.floor(r/3600)," hours ago"):r<2592e3?"".concat(Math.floor(r/86400)," days ago"):r<31536e3?"".concat(Math.floor(r/2592e3)," months ago"):"".concat(Math.floor(r/31536e3)," years ago")}function i(e){let t=Math.floor(e),r=e%1>=.5;return"★".repeat(t)+(r?"☆":"")+"☆".repeat(5-t-!!r)}r.d(t,{cn:()=>n,vv:()=>o,XK:()=>i,IM:()=>s,gs:()=>c,Yv:()=>a})},2664:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=r(9991),o=r(7102);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},2757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return c},urlObjectKeys:function(){return s}});let n=r(6966)._(r(8859)),o=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:r}=e,a=e.protocol||"",s=e.pathname||"",c=e.hash||"",i=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),i&&"object"==typeof i&&(i=String(n.urlQueryToSearchParams(i)));let l=e.search||i&&"?"+i||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||o.test(a))&&!1!==u?(u="//"+(u||""),s&&"/"!==s[0]&&(s="/"+s)):u||(u=""),c&&"#"!==c[0]&&(c="#"+c),l&&"?"!==l[0]&&(l="?"+l),""+a+u+(s=s.replace(/[?#]/g,encodeURIComponent))+(l=l.replace("#","%23"))+c}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function c(e){return a(e)}},3180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},3741:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(5155),o=r(2115),a=r(1380);let s=o.forwardRef((e,t)=>{let{variant:r="primary",size:o="md",children:s,className:c,loading:i=!1,disabled:u,...l}=e;return(0,n.jsxs)("button",{ref:t,className:(0,a.cn)("\n      inline-flex items-center justify-center font-medium transition-default\n      focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\n      disabled:opacity-50 disabled:cursor-not-allowed\n    ",{primary:"\n        bg-[var(--color-accent-brown)] text-white border-none\n        hover:bg-[var(--color-accent-warm-brown)] hover:scale-105\n        active:bg-[var(--color-accent-light-brown)]\n      ",secondary:"\n        bg-transparent text-[var(--color-accent-brown)] \n        border-2 border-[var(--color-accent-brown)]\n        hover:bg-[var(--color-accent-brown)] hover:text-white hover:scale-105\n        active:bg-[var(--color-accent-warm-brown)]\n      ",outline:"\n        bg-transparent text-[var(--color-black)] \n        border border-gray-300\n        hover:bg-gray-50 hover:border-gray-400\n        active:bg-gray-100\n      ",ghost:"\n        bg-transparent text-[var(--color-black)] border-none\n        hover:bg-gray-100\n        active:bg-gray-200\n      "}[r],{sm:"px-4 py-2 text-sm rounded-full",md:"px-8 py-4 text-base rounded-full",lg:"px-10 py-5 text-lg rounded-full"}[o],c),disabled:u||i,...l,children:[i&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s]})});s.displayName="Button";let c=s},5430:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(5155),o=r(2115),a=r(6874),s=r.n(a),c=r(5695),i=r(1380),u=r(3741);let l=e=>{let{className:t}=e,[r,a]=(0,o.useState)(!1),[l,f]=(0,o.useState)(!1),d=(0,c.usePathname)();(0,o.useEffect)(()=>{let e=()=>{a(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let p=[{name:"Home",href:"/"},{name:"Colleges",href:"/colleges"},{name:"Compare",href:"/compare"},{name:"Community",href:"/community"},{name:"About",href:"/about"}];return(0,n.jsx)("header",{className:(0,i.cn)("fixed top-0 left-0 right-0 z-50 transition-default",r?"bg-white/95 backdrop-blur-sm shadow-md":"bg-transparent",t),children:(0,n.jsxs)("div",{className:"container-max-width",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between h-20 px-6",children:[(0,n.jsxs)(s(),{href:"/",className:"flex items-center space-x-2 text-xl font-bold text-[var(--color-black)] hover:text-[var(--color-accent-brown)] transition-default",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-[var(--color-accent-brown)] rounded-full flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-white font-bold text-sm",children:"CF"})}),(0,n.jsx)("span",{children:"College Finder"})]}),(0,n.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:p.map(e=>(0,n.jsx)(s(),{href:e.href,className:(0,i.cn)("text-sm font-medium transition-default hover:text-[var(--color-accent-brown)]",d===e.href?"text-[var(--color-accent-brown)]":"text-[var(--color-black)]"),children:e.name},e.name))}),(0,n.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,n.jsx)(u.A,{variant:"secondary",size:"sm",children:"Sign In"}),(0,n.jsx)(u.A,{variant:"primary",size:"sm",children:"Get Started"})]}),(0,n.jsx)("button",{className:"md:hidden p-2 rounded-full bg-[var(--color-black)] text-white hover:bg-[var(--color-accent-brown)] transition-default",onClick:()=>f(!l),"aria-label":"Toggle mobile menu",children:(0,n.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l?(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]}),l&&(0,n.jsx)("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,n.jsxs)("div",{className:"px-6 py-4 space-y-4",children:[p.map(e=>(0,n.jsx)(s(),{href:e.href,className:(0,i.cn)("block text-base font-medium transition-default hover:text-[var(--color-accent-brown)]",d===e.href?"text-[var(--color-accent-brown)]":"text-[var(--color-black)]"),onClick:()=>f(!1),children:e.name},e.name)),(0,n.jsxs)("div",{className:"pt-4 space-y-3",children:[(0,n.jsx)(u.A,{variant:"secondary",size:"sm",className:"w-full",children:"Sign In"}),(0,n.jsx)(u.A,{variant:"primary",size:"sm",className:"w-full",children:"Get Started"})]})]})})]})})}},5695:(e,t,r)=>{"use strict";var n=r(8999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(2115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},useLinkStatus:function(){return y}});let n=r(6966),o=r(5155),a=n._(r(2115)),s=r(2757),c=r(5227),i=r(9818),u=r(6654),l=r(9991),f=r(5929);r(3230);let d=r(4930),p=r(2664),h=r(6634);function m(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function g(e){let t,r,n,[s,g]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),y=(0,a.useRef)(null),{href:v,as:x,children:w,prefetch:j=null,passHref:P,replace:N,shallow:_,scroll:C,onClick:E,onMouseEnter:k,onTouchStart:O,legacyBehavior:M=!1,onNavigate:A,ref:S,unstable_dynamicOnHover:L,...T}=e;t=w,M&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let I=a.default.useContext(c.AppRouterContext),R=!1!==j,U=null===j||"auto"===j?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:F,as:D}=a.default.useMemo(()=>{let e=m(v);return{href:e,as:x?m(x):e}},[v,x]);M&&(r=a.default.Children.only(t));let z=M?r&&"object"==typeof r&&r.ref:S,K=a.default.useCallback(e=>(null!==I&&(y.current=(0,d.mountLinkInstance)(e,F,I,U,R,g)),()=>{y.current&&((0,d.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,d.unmountPrefetchableInstance)(e)}),[R,F,I,U,g]),B={ref:(0,u.useMergedRef)(K,z),onClick(e){M||"function"!=typeof E||E(e),M&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,n,o,s,c){let{nodeName:i}=e.currentTarget;if(!("A"===i.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),c){let e=!1;if(c({preventDefault:()=>{e=!0}}),e)return}a.default.startTransition(()=>{(0,h.dispatchNavigateAction)(r||t,o?"replace":"push",null==s||s,n.current)})}}(e,F,D,y,N,C,A))},onMouseEnter(e){M||"function"!=typeof k||k(e),M&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),I&&R&&(0,d.onNavigationIntent)(e.currentTarget,!0===L)},onTouchStart:function(e){M||"function"!=typeof O||O(e),M&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),I&&R&&(0,d.onNavigationIntent)(e.currentTarget,!0===L)}};return(0,l.isAbsoluteUrl)(D)?B.href=D:M&&!P&&("a"!==r.type||"href"in r.props)||(B.href=(0,f.addBasePath)(D)),n=M?a.default.cloneElement(r,B):(0,o.jsx)("a",{...T,...B,children:t}),(0,o.jsx)(b.Provider,{value:s,children:n})}r(3180);let b=(0,a.createContext)(d.IDLE_LINK_STATUS),y=()=>(0,a.useContext)(b);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[r,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(r,n(e));else t.set(r,n(o));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return b},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return i},getLocationOrigin:function(){return s},getURL:function(){return c},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return v}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>o.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function c(){let{href:e}=window.location,t=s();return e.substring(t.length)}function i(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function l(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+i(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function v(e){return JSON.stringify({message:e.message,stack:e.stack})}}},e=>{e.O(0,[690,441,964,358],()=>e(e.s=260)),_N_E=e.O()}]);