'use client';

import React, { useState } from 'react';
import { Section, SectionTitle, Grid } from '@/components/ui/Section';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { mockQuestions, mockPolls, getPopularQuestions, getRecentQuestions, getActivePolls } from '@/data/community';
import { getInitials, getRelativeTime } from '@/utils/cn';

type TabType = 'questions' | 'polls' | 'popular' | 'recent';

const CommunityPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('questions');

  const tabs = [
    { id: 'questions', label: 'All Questions', count: mockQuestions.length },
    { id: 'popular', label: 'Popular', count: getPopularQuestions().length },
    { id: 'recent', label: 'Recent', count: getRecentQuestions().length },
    { id: 'polls', label: 'Polls', count: getActivePolls().length }
  ];

  const getQuestionsToShow = () => {
    switch (activeTab) {
      case 'popular':
        return getPopularQuestions(10);
      case 'recent':
        return getRecentQuestions(10);
      case 'questions':
      default:
        return mockQuestions;
    }
  };

  const handleVote = (questionId: string, isUpvote: boolean) => {
    // In a real app, this would make an API call
    console.log(`${isUpvote ? 'Upvoted' : 'Downvoted'} question ${questionId}`);
  };

  const handlePollVote = (pollId: string, optionId: string) => {
    // In a real app, this would make an API call
    console.log(`Voted for option ${optionId} in poll ${pollId}`);
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <Section background="dark" padding="lg">
        <div className="text-center text-white">
          <SectionTitle size="lg" className="text-white mb-6">
            College Community
          </SectionTitle>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            Connect with students, alumni, and experts. Ask questions, share experiences, 
            and get insights about college life, academics, and career opportunities.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="primary" size="lg">
              Ask a Question
            </Button>
            <Button variant="secondary" size="lg" className="bg-transparent border-white text-white hover:bg-white hover:text-black">
              Create Poll
            </Button>
          </div>
        </div>
      </Section>

      {/* Navigation Tabs */}
      <Section background="light" padding="sm">
        <div className="flex flex-wrap gap-2 justify-center">
          {tabs.map((tab) => (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "primary" : "outline"}
              size="sm"
              onClick={() => setActiveTab(tab.id as TabType)}
            >
              {tab.label} ({tab.count})
            </Button>
          ))}
        </div>
      </Section>

      {/* Content */}
      <Section background="white" padding="lg">
        {activeTab === 'polls' ? (
          <div className="space-y-6">
            <SectionTitle size="md">Active Polls</SectionTitle>
            <div className="space-y-6">
              {getActivePolls().map((poll) => (
                <Card key={poll.id}>
                  <CardHeader>
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold">
                        {getInitials(poll.userName)}
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-lg">{poll.title}</CardTitle>
                        <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                          <span>by {poll.userName}</span>
                          <span>•</span>
                          <span>{getRelativeTime(poll.date)}</span>
                          <span>•</span>
                          <span>{poll.totalVotes} votes</span>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-6">{poll.description}</p>
                    
                    <div className="space-y-3">
                      {poll.options.map((option) => {
                        const percentage = poll.totalVotes > 0 ? (option.votes / poll.totalVotes) * 100 : 0;
                        return (
                          <div
                            key={option.id}
                            className="relative cursor-pointer group"
                            onClick={() => handlePollVote(poll.id, option.id)}
                          >
                            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-[var(--color-accent-brown)] transition-default">
                              <span className="font-medium">{option.text}</span>
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-gray-600">{option.votes} votes</span>
                                <span className="text-sm font-bold text-[var(--color-accent-brown)]">
                                  {percentage.toFixed(1)}%
                                </span>
                              </div>
                            </div>
                            <div
                              className="absolute left-0 top-0 bottom-0 bg-[var(--color-accent-brown)]/10 rounded-lg transition-all duration-300"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        );
                      })}
                    </div>

                    {poll.tags && poll.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-4">
                        {poll.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <SectionTitle size="md">
              {activeTab === 'popular' ? 'Popular Questions' : 
               activeTab === 'recent' ? 'Recent Questions' : 'All Questions'}
            </SectionTitle>
            
            <div className="space-y-6">
              {getQuestionsToShow().map((question) => (
                <Card key={question.id} className="hover:shadow-lg transition-default">
                  <CardContent>
                    <div className="flex gap-4">
                      {/* Vote Section */}
                      <div className="flex flex-col items-center gap-2 min-w-[60px]">
                        <button
                          onClick={() => handleVote(question.id, true)}
                          className="p-2 rounded-full hover:bg-gray-100 transition-default"
                        >
                          <svg className="w-5 h-5 text-gray-600 hover:text-[var(--color-accent-brown)]" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                          </svg>
                        </button>
                        <span className="font-bold text-[var(--color-accent-brown)]">
                          {question.votes}
                        </span>
                        <button
                          onClick={() => handleVote(question.id, false)}
                          className="p-2 rounded-full hover:bg-gray-100 transition-default"
                        >
                          <svg className="w-5 h-5 text-gray-600 hover:text-[var(--color-accent-brown)]" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 20l1.41-1.41L7.83 13H20v-2H7.83l5.58-5.59L12 4l-8 8z"/>
                          </svg>
                        </button>
                      </div>

                      {/* Question Content */}
                      <div className="flex-1">
                        <div className="flex items-start gap-4 mb-3">
                          <div className="w-10 h-10 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold">
                            {getInitials(question.userName)}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-[var(--color-black)] mb-2 hover:text-[var(--color-accent-brown)] cursor-pointer">
                              {question.title}
                            </h3>
                            <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                              <span>by {question.userName}</span>
                              <span>•</span>
                              <span>{getRelativeTime(question.date)}</span>
                              <span>•</span>
                              <span>{question.views} views</span>
                              {question.isResolved && (
                                <>
                                  <span>•</span>
                                  <span className="text-green-600 font-medium">✓ Resolved</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>

                        <p className="text-gray-700 mb-4 leading-relaxed">
                          {question.content}
                        </p>

                        {/* Tags */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {question.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] text-sm rounded-full cursor-pointer hover:bg-[var(--color-accent-brown)]/20"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>

                        {/* Answer Summary */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>{question.answers.length} answers</span>
                            {question.answers.some(a => a.isAccepted) && (
                              <span className="text-green-600 font-medium">✓ Has accepted answer</span>
                            )}
                          </div>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </div>

                        {/* Top Answer Preview */}
                        {question.answers.length > 0 && (
                          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <div className="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold">
                                {getInitials(question.answers[0].userName)}
                              </div>
                              <span className="text-sm font-medium">{question.answers[0].userName}</span>
                              {question.answers[0].isAccepted && (
                                <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                  ✓ Accepted
                                </span>
                              )}
                              <span className="text-xs text-gray-500">
                                {question.answers[0].votes} votes
                              </span>
                            </div>
                            <p className="text-sm text-gray-700">
                              {question.answers[0].content.length > 150
                                ? `${question.answers[0].content.substring(0, 150)}...`
                                : question.answers[0].content
                              }
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}
      </Section>
    </div>
  );
};

export default CommunityPage;
