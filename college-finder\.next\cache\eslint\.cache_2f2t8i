[{"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\CounselingCategories.tsx": "3", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\FeaturedColleges.tsx": "4", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\HeroSection.tsx": "5", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\StatsSection.tsx": "6", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\layout\\Header.tsx": "7", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\Button.tsx": "8", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\Card.tsx": "9", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\PlaceholderImage.tsx": "10", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\Section.tsx": "11", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\data\\colleges.ts": "12", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\types\\index.ts": "13", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\utils\\cn.ts": "14", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\college\\[id]\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\colleges\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\community\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\compare\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\SearchFilters.tsx": "19", "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\data\\community.ts": "20"}, {"size": 715, "mtime": 1755514270672, "results": "21", "hashOfConfig": "22"}, {"size": 477, "mtime": 1755514251444, "results": "23", "hashOfConfig": "22"}, {"size": 6363, "mtime": 1755514336187, "results": "24", "hashOfConfig": "22"}, {"size": 8841, "mtime": 1755514790944, "results": "25", "hashOfConfig": "22"}, {"size": 6615, "mtime": 1755514484560, "results": "26", "hashOfConfig": "22"}, {"size": 7212, "mtime": 1755514404792, "results": "27", "hashOfConfig": "22"}, {"size": 4970, "mtime": 1755514150419, "results": "28", "hashOfConfig": "22"}, {"size": 2767, "mtime": 1755514058605, "results": "29", "hashOfConfig": "22"}, {"size": 4414, "mtime": 1755514113170, "results": "30", "hashOfConfig": "22"}, {"size": 1539, "mtime": 1755514465561, "results": "31", "hashOfConfig": "22"}, {"size": 4174, "mtime": 1755514132157, "results": "32", "hashOfConfig": "22"}, {"size": 28802, "mtime": 1755515010669, "results": "33", "hashOfConfig": "22"}, {"size": 5840, "mtime": 1755514040140, "results": "34", "hashOfConfig": "22"}, {"size": 2463, "mtime": 1755514072020, "results": "35", "hashOfConfig": "22"}, {"size": 25408, "mtime": 1755516231110, "results": "36", "hashOfConfig": "22"}, {"size": 13586, "mtime": 1755516359146, "results": "37", "hashOfConfig": "22"}, {"size": 13351, "mtime": 1755515286572, "results": "38", "hashOfConfig": "22"}, {"size": 12936, "mtime": 1755515331940, "results": "39", "hashOfConfig": "22"}, {"size": 10094, "mtime": 1755516291733, "results": "40", "hashOfConfig": "22"}, {"size": 7389, "mtime": 1755515053142, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d42q0x", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\CounselingCategories.tsx", ["102"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\FeaturedColleges.tsx", ["103", "104"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\HeroSection.tsx", ["105", "106"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\StatsSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\Card.tsx", ["107"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\PlaceholderImage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\ui\\Section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\data\\colleges.ts", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\utils\\cn.ts", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\college\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\colleges\\page.tsx", ["108"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\community\\page.tsx", ["109", "110"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\compare\\page.tsx", ["111", "112"], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\components\\features\\SearchFilters.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\data\\community.ts", ["113", "114"], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 4, "column": 8, "nodeType": null, "messageId": "117", "endLine": 4, "endColumn": 12}, {"ruleId": "115", "severity": 1, "message": "116", "line": 4, "column": 8, "nodeType": null, "messageId": "117", "endLine": 4, "endColumn": 12}, {"ruleId": "115", "severity": 1, "message": "118", "line": 11, "column": 46, "nodeType": null, "messageId": "117", "endLine": 11, "endColumn": 53}, {"ruleId": "115", "severity": 1, "message": "119", "line": 4, "column": 8, "nodeType": null, "messageId": "117", "endLine": 4, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "120", "line": 7, "column": 10, "nodeType": null, "messageId": "117", "endLine": 7, "endColumn": 12}, {"ruleId": "121", "severity": 1, "message": "122", "line": 191, "column": 9, "nodeType": "123", "endLine": 200, "endColumn": 11}, {"ruleId": "115", "severity": 1, "message": "124", "line": 11, "column": 10, "nodeType": null, "messageId": "117", "endLine": 11, "endColumn": 17}, {"ruleId": "115", "severity": 1, "message": "125", "line": 4, "column": 33, "nodeType": null, "messageId": "117", "endLine": 4, "endColumn": 37}, {"ruleId": "115", "severity": 1, "message": "126", "line": 7, "column": 25, "nodeType": null, "messageId": "117", "endLine": 7, "endColumn": 34}, {"ruleId": "115", "severity": 1, "message": "127", "line": 5, "column": 29, "nodeType": null, "messageId": "117", "endLine": 5, "endColumn": 39}, {"ruleId": "115", "severity": 1, "message": "128", "line": 5, "column": 41, "nodeType": null, "messageId": "117", "endLine": 5, "endColumn": 50}, {"ruleId": "115", "severity": 1, "message": "129", "line": 1, "column": 20, "nodeType": null, "messageId": "117", "endLine": 1, "endColumn": 26}, {"ruleId": "115", "severity": 1, "message": "130", "line": 1, "column": 34, "nodeType": null, "messageId": "117", "endLine": 1, "endColumn": 44}, "@typescript-eslint/no-unused-vars", "'Link' is defined but never used.", "unusedVar", "'slugify' is defined but never used.", "'Image' is defined but never used.", "'cn' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'College' is defined but never used.", "'Grid' is defined but never used.", "'mockPolls' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", "'Answer' is defined but never used.", "'PollOption' is defined but never used."]