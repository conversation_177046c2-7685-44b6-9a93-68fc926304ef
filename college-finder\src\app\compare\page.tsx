'use client';

import React, { useState } from 'react';
import { Section, SectionTitle } from '@/components/ui/Section';
import { Card, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import PlaceholderImage from '@/components/ui/PlaceholderImage';
import { mockColleges } from '@/data/colleges';
import { College } from '@/types';
import { formatCurrency, generateStarRating } from '@/utils/cn';

const ComparePage: React.FC = () => {
  const [selectedColleges, setSelectedColleges] = useState<College[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredColleges = mockColleges.filter(college =>
    college.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    college.location.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
    college.location.state.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddCollege = (college: College) => {
    if (selectedColleges.length < 3 && !selectedColleges.find(c => c.id === college.id)) {
      setSelectedColleges([...selectedColleges, college]);
    }
  };

  const handleRemoveCollege = (collegeId: string) => {
    setSelectedColleges(selectedColleges.filter(c => c.id !== collegeId));
  };

  const comparisonRows = [
    {
      category: 'Basic Information',
      items: [
        { label: 'Established Year', getValue: (college: College) => college.establishedYear },
        { label: 'Type', getValue: (college: College) => college.type },
        { label: 'Location', getValue: (college: College) => `${college.location.city}, ${college.location.state}` },
        { label: 'NIRF Ranking', getValue: (college: College) => college.nirfRanking ? `#${college.nirfRanking}` : 'Not Ranked' },
        { label: 'Counseling Category', getValue: (college: College) => college.counselingCategory }
      ]
    },
    {
      category: 'Academics & Placements',
      items: [
        { label: 'Departments', getValue: (college: College) => college.academics.departments.length },
        { label: 'Placement Rate', getValue: (college: College) => `${college.academics.placementStats.placementPercentage}%` },
        { label: 'Average Package', getValue: (college: College) => formatCurrency(college.academics.placementStats.averagePackage) },
        { label: 'Highest Package', getValue: (college: College) => formatCurrency(college.academics.placementStats.highestPackage) },
        { label: 'Research Papers', getValue: (college: College) => college.academics.researchPapers }
      ]
    },
    {
      category: 'Ratings',
      items: [
        { label: 'Overall Rating', getValue: (college: College) => `${college.rating.overall} ${generateStarRating(college.rating.overall)}` },
        { label: 'Academic Rating', getValue: (college: College) => `${college.rating.academics} ${generateStarRating(college.rating.academics)}` },
        { label: 'Infrastructure Rating', getValue: (college: College) => `${college.rating.infrastructure} ${generateStarRating(college.rating.infrastructure)}` },
        { label: 'Placement Rating', getValue: (college: College) => `${college.rating.placements} ${generateStarRating(college.rating.placements)}` },
        { label: 'Culture Rating', getValue: (college: College) => `${college.rating.culture} ${generateStarRating(college.rating.culture)}` }
      ]
    },
    {
      category: 'Campus Life',
      items: [
        { label: 'Hostels', getValue: (college: College) => college.hostels.length },
        { label: 'Festivals', getValue: (college: College) => college.fests.length },
        { label: 'Clubs', getValue: (college: College) => college.clubs.length },
        { label: 'Reviews', getValue: (college: College) => college.reviews.length }
      ]
    }
  ];

  return (
    <div className="min-h-screen pt-20">
      {/* Hero Section */}
      <Section background="light" padding="lg">
        <div className="text-center mb-8">
          <SectionTitle size="lg" className="mb-4">
            Compare Engineering Colleges
          </SectionTitle>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Compare up to 3 colleges side-by-side to make an informed decision about your engineering education.
          </p>
        </div>

        {/* Selected Colleges */}
        {selectedColleges.length > 0 && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">Selected Colleges ({selectedColleges.length}/3)</h3>
            <div className="flex flex-wrap gap-4">
              {selectedColleges.map((college) => (
                <div key={college.id} className="flex items-center gap-2 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] px-4 py-2 rounded-full">
                  <span className="font-medium">{college.name}</span>
                  <button
                    onClick={() => handleRemoveCollege(college.id)}
                    className="hover:bg-[var(--color-accent-brown)]/20 rounded-full p-1"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Search Bar */}
        <div className="max-w-2xl mx-auto">
          <input
            type="text"
            placeholder="Search colleges to add for comparison..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"
          />
        </div>
      </Section>

      {/* Search Results */}
      {searchQuery && (
        <Section background="white" padding="md">
          <h3 className="text-lg font-semibold mb-4">Search Results</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredColleges.slice(0, 6).map((college) => {
              const isSelected = selectedColleges.find(c => c.id === college.id);
              const canAdd = selectedColleges.length < 3 && !isSelected;
              
              return (
                <Card key={college.id} className={isSelected ? 'border-[var(--color-accent-brown)] bg-[var(--color-accent-brown)]/5' : ''}>
                  <CardContent>
                    <div className="flex items-center gap-3 mb-3">
                      <PlaceholderImage
                        text={college.name.substring(0, 2)}
                        width={50}
                        height={50}
                        className="rounded-lg"
                        color="brown"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold text-sm">{college.name}</h4>
                        <p className="text-xs text-gray-600">{college.location.city}, {college.location.state}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <span className="text-yellow-500 text-sm">{generateStarRating(college.rating.overall)}</span>
                        <span className="text-sm font-medium">{college.rating.overall}</span>
                      </div>
                      
                      {isSelected ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveCollege(college.id)}
                        >
                          Remove
                        </Button>
                      ) : (
                        <Button
                          variant={canAdd ? "primary" : "outline"}
                          size="sm"
                          disabled={!canAdd}
                          onClick={() => handleAddCollege(college)}
                        >
                          {canAdd ? 'Add' : 'Max 3'}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </Section>
      )}

      {/* Comparison Table */}
      {selectedColleges.length >= 2 && (
        <Section background="white" padding="lg">
          <SectionTitle size="md" className="mb-8">College Comparison</SectionTitle>
          
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              {/* Header */}
              <thead>
                <tr>
                  <th className="text-left p-4 border-b-2 border-gray-200 w-64">
                    <div className="font-semibold text-gray-700">Comparison Criteria</div>
                  </th>
                  {selectedColleges.map((college) => (
                    <th key={college.id} className="text-center p-4 border-b-2 border-gray-200 min-w-64">
                      <div className="space-y-3">
                        <PlaceholderImage
                          text={college.name}
                          aspectRatio="16/9"
                          color="brown"
                          className="rounded-lg"
                        />
                        <div>
                          <div className="font-semibold text-[var(--color-accent-brown)]">{college.name}</div>
                          <div className="text-sm text-gray-600">{college.location.city}, {college.location.state}</div>
                        </div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>

              {/* Body */}
              <tbody>
                {comparisonRows.map((section) => (
                  <React.Fragment key={section.category}>
                    <tr>
                      <td colSpan={selectedColleges.length + 1} className="p-4 bg-gray-50 border-b border-gray-200">
                        <div className="font-semibold text-[var(--color-accent-brown)]">{section.category}</div>
                      </td>
                    </tr>
                    {section.items.map((item) => (
                      <tr key={item.label} className="hover:bg-gray-50">
                        <td className="p-4 border-b border-gray-100 font-medium text-gray-700">
                          {item.label}
                        </td>
                        {selectedColleges.map((college) => (
                          <td key={college.id} className="p-4 border-b border-gray-100 text-center">
                            <div className="font-medium">{item.getValue(college)}</div>
                          </td>
                        ))}
                      </tr>
                    ))}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 mt-8">
            <Button variant="primary" size="lg">
              Download Comparison
            </Button>
            <Button variant="secondary" size="lg">
              Share Comparison
            </Button>
          </div>
        </Section>
      )}

      {/* Empty State */}
      {selectedColleges.length === 0 && !searchQuery && (
        <Section background="white" padding="lg">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⚖️</div>
            <h3 className="text-2xl font-bold text-gray-700 mb-2">Start Comparing Colleges</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Search and select up to 3 colleges to compare their features, ratings, and statistics side-by-side.
            </p>
            <Button
              variant="primary"
              onClick={() => setSearchQuery('IIT')}
            >
              Search Colleges
            </Button>
          </div>
        </Section>
      )}

      {/* Need More Colleges State */}
      {selectedColleges.length === 1 && (
        <Section background="light" padding="md">
          <div className="text-center">
            <p className="text-gray-600 mb-4">
              Add at least one more college to start comparing
            </p>
            <Button
              variant="outline"
              onClick={() => setSearchQuery('')}
            >
              Search More Colleges
            </Button>
          </div>
        </Section>
      )}
    </div>
  );
};

export default ComparePage;
