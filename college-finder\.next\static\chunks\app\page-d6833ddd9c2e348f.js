(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1446:(e,s,l)=>{"use strict";l.d(s,{default:()=>o});var t=l(5155),r=l(2115),a=l(1514),c=l(3741),n=l(5695);let i=e=>{let{value:s,label:l,suffix:a="",prefix:c="",duration:n=2e3}=e,[i,o]=(0,r.useState)(0),[d,x]=(0,r.useState)(!1),m=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let e=new IntersectionObserver(e=>{let[s]=e;s.isIntersecting&&x(!0)},{threshold:.1});return m.current&&e.observe(m.current),()=>e.disconnect()},[]),(0,r.useEffect)(()=>{if(!d)return;let e=Date.now(),l=()=>{let t=Math.min((Date.now()-e)/n,1);o(Math.floor(0+(s-0)*(1-Math.pow(1-t,4)))),t<1&&requestAnimationFrame(l)};requestAnimationFrame(l)},[d,s,n]),(0,t.jsxs)("div",{ref:m,className:"text-center",children:[(0,t.jsxs)("div",{className:"text-4xl md:text-5xl font-bold text-[var(--color-accent-brown)] mb-2",children:[c,i.toLocaleString("en-IN"),a]}),(0,t.jsx)("div",{className:"text-gray-600 text-lg font-medium",children:l})]})},o=()=>{let e=(0,n.useRouter)(),s=[{icon:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),title:"Verified Information",description:"All college data is verified and regularly updated by our research team"},{icon:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"})}),title:"Comprehensive Details",description:"Get complete information about academics, hostels, fests, clubs, and placements"},{icon:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z"})}),title:"Smart Comparison",description:"Compare multiple colleges side-by-side with detailed metrics and analytics"},{icon:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}),title:"Expert Guidance",description:"Get personalized recommendations based on your preferences and goals"}];return(0,t.jsxs)(a.wn,{background:"white",padding:"xl",id:"stats",children:[(0,t.jsxs)("div",{className:"mb-20",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)(a._x,{size:"lg",className:"mb-6",children:"Trusted by Students Across India"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Join thousands of students who have made informed college decisions with our platform"})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12",children:[{value:1e3,label:"Engineering Colleges",suffix:"+"},{value:5e4,label:"Student Reviews",suffix:"+"},{value:500,label:"Cities Covered",suffix:"+"},{value:95,label:"Success Rate",suffix:"%"}].map((e,s)=>(0,t.jsx)(i,{value:e.value,label:e.label,suffix:e.suffix,duration:2e3+200*s},s))})]}),(0,t.jsxs)("div",{className:"mb-16",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-[var(--color-black)] mb-4",children:"Why Choose College Finder?"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"We provide the most comprehensive and reliable college information platform in India"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"text-center group",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-[var(--color-accent-brown)]/10 rounded-full flex items-center justify-center mx-auto mb-4 text-[var(--color-accent-brown)] group-hover:bg-[var(--color-accent-brown)] group-hover:text-white transition-default",children:e.icon}),(0,t.jsx)("h4",{className:"text-xl font-semibold text-[var(--color-black)] mb-3",children:e.title}),(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]},s))})]}),(0,t.jsxs)("div",{className:"text-center bg-gradient-to-r from-[var(--color-accent-brown)]/5 to-[var(--color-accent-warm-brown)]/5 rounded-2xl p-12",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-[var(--color-black)] mb-4",children:"Ready to Find Your Perfect College?"}),(0,t.jsx)("p",{className:"text-lg text-gray-600 mb-8 max-w-2xl mx-auto",children:"Start your journey today and discover the engineering college that matches your dreams and aspirations."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(c.A,{variant:"primary",size:"lg",onClick:()=>e.push("/colleges"),className:"hover-button-scale",children:"Explore Colleges Now"}),(0,t.jsx)(c.A,{variant:"secondary",size:"lg",onClick:()=>e.push("/compare"),className:"hover-button-scale",children:"Compare Colleges"})]})]})]})}},3081:(e,s,l)=>{Promise.resolve().then(l.bind(l,8351)),Promise.resolve().then(l.bind(l,6648)),Promise.resolve().then(l.bind(l,8801)),Promise.resolve().then(l.bind(l,1446))},5695:(e,s,l)=>{"use strict";var t=l(8999);l.o(t,"useParams")&&l.d(s,{useParams:function(){return t.useParams}}),l.o(t,"usePathname")&&l.d(s,{usePathname:function(){return t.usePathname}}),l.o(t,"useRouter")&&l.d(s,{useRouter:function(){return t.useRouter}}),l.o(t,"useSearchParams")&&l.d(s,{useSearchParams:function(){return t.useSearchParams}})},6648:(e,s,l)=>{"use strict";l.d(s,{default:()=>x});var t=l(5155);l(2115);var r=l(5695),a=l(7703),c=l(1514),n=l(3741),i=l(9671),o=l(4028),d=l(1380);let x=()=>{let e=(0,r.useRouter)(),s=o.$.slice(0,3);return(0,t.jsxs)(c.wn,{background:"dark",padding:"xl",id:"featured-colleges",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)(c._x,{size:"lg",className:"text-white mb-6",children:"Featured Engineering Colleges"}),(0,t.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed",children:"Discover top-rated engineering colleges with excellent academics, infrastructure, and placement records."})]}),(0,t.jsx)(c.xA,{columns:3,gap:"lg",className:"mb-12",children:s.map(s=>(0,t.jsxs)(a.Zp,{hover:!0,padding:"none",className:"group cursor-pointer bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20",onClick:()=>{var l;return l=s.id,void e.push("/college/".concat(l))},children:[(0,t.jsx)(i.A,{text:"".concat(s.name," Campus"),aspectRatio:"16/9",color:"brown",className:"group-hover:scale-105"}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)(a.aR,{children:(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(a.ZB,{className:"text-white group-hover:text-[var(--color-accent-brown)] transition-default text-lg leading-tight",children:s.name}),(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,t.jsxs)("span",{className:"text-gray-300 text-sm",children:[s.location.city,", ",s.location.state]}),(0,t.jsx)("span",{className:"text-gray-400",children:"•"}),(0,t.jsx)("span",{className:"text-[var(--color-accent-brown)] text-sm font-medium",children:s.type})]})]}),s.nirfRanking&&(0,t.jsxs)("div",{className:"bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold",children:["#",s.nirfRanking]})]})}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed mb-4",children:s.description}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"text-center p-3 bg-white/5 rounded-lg",children:[(0,t.jsxs)("div",{className:"text-[var(--color-accent-brown)] font-bold text-lg",children:[s.academics.placementStats.placementPercentage,"%"]}),(0,t.jsx)("div",{className:"text-gray-400 text-xs",children:"Placement"})]}),(0,t.jsxs)("div",{className:"text-center p-3 bg-white/5 rounded-lg",children:[(0,t.jsx)("div",{className:"text-[var(--color-accent-brown)] font-bold text-lg",children:(0,d.vv)(s.academics.placementStats.averagePackage)}),(0,t.jsx)("div",{className:"text-gray-400 text-xs",children:"Avg Package"})]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"text-yellow-400 text-lg",children:(0,d.XK)(s.rating.overall)}),(0,t.jsx)("span",{className:"text-white font-medium",children:s.rating.overall})]}),(0,t.jsxs)("span",{className:"text-gray-400 text-sm",children:[s.reviews.length," reviews"]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("span",{className:"inline-block bg-[var(--color-accent-brown)]/20 text-[var(--color-accent-brown)] px-3 py-1 rounded-full text-sm font-medium",children:s.counselingCategory})}),(0,t.jsxs)(n.A,{variant:"secondary",size:"sm",className:"w-full bg-transparent border-white/30 text-white hover:bg-[var(--color-accent-brown)] hover:border-[var(--color-accent-brown)]",children:["View Details",(0,t.jsx)("svg",{className:"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]})]},s.id))}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)(n.A,{variant:"primary",size:"lg",onClick:()=>{e.push("/colleges")},className:"hover-button-scale",children:["View All Colleges",(0,t.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7l5 5m0 0l-5 5m5-5H6"})})]})}),(0,t.jsxs)("div",{className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-[var(--color-accent-brown)]/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-[var(--color-accent-brown)]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Verified Reviews"}),(0,t.jsx)("p",{className:"text-gray-300 text-sm",children:"Read authentic reviews from current students and alumni"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-[var(--color-accent-brown)]/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-[var(--color-accent-brown)]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H15V1h-2v1H9V1H7v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2z"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Detailed Analytics"}),(0,t.jsx)("p",{className:"text-gray-300 text-sm",children:"Compare placement stats, fees, and academic performance"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-[var(--color-accent-brown)]/20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("svg",{className:"w-8 h-8 text-[var(--color-accent-brown)]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z"})})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"Easy Comparison"}),(0,t.jsx)("p",{className:"text-gray-300 text-sm",children:"Side-by-side comparison of multiple colleges"})]})]})]})}},8351:(e,s,l)=>{"use strict";l.d(s,{default:()=>d});var t=l(5155);l(2115);var r=l(5695),a=l(7703),c=l(1514),n=l(3741),i=l(4028),o=l(1380);let d=()=>{let e=(0,r.useRouter)(),s={"JOSAA/CSAB":(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"})}),"JAC Delhi":(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})}),"COMEDK/KCET":(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"})}),MHTCET:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})}),WBJEE:(0,t.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H15V1h-2v1H9V1H7v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2z"})})};return(0,t.jsxs)(c.wn,{background:"light",padding:"xl",id:"counseling-categories",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)(c._x,{size:"lg",className:"mb-6",children:"Choose Your Counseling Category"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Find colleges based on your entrance exam and counseling process. Each category covers specific states and examination boards."})]}),(0,t.jsx)(c.xA,{columns:3,gap:"lg",className:"mb-12",children:i.K.map(l=>(0,t.jsxs)(a.Zp,{hover:!0,className:"group cursor-pointer border-2 border-transparent hover:border-[var(--color-accent-brown)]/20",onClick:()=>(s=>{let l=(0,o.Yv)(s);e.push("/counseling/".concat(l))})(l.name),children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-[var(--color-accent-brown)]/10 rounded-full flex items-center justify-center text-[var(--color-accent-brown)] group-hover:bg-[var(--color-accent-brown)] group-hover:text-white transition-default",children:s[l.name]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-[var(--color-accent-brown)]",children:l.collegeCount}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Colleges"})]})]}),(0,t.jsx)(a.ZB,{className:"group-hover:text-[var(--color-accent-brown)] transition-default",children:l.name})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4 leading-relaxed",children:l.description}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"Coverage:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:l.states.map(e=>(0,t.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full",children:e},e))})]}),(0,t.jsxs)(n.A,{variant:"secondary",size:"sm",className:"w-full group-hover:bg-[var(--color-accent-brown)] group-hover:text-white group-hover:border-[var(--color-accent-brown)]",children:["Explore Colleges",(0,t.jsx)("svg",{className:"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]},l.name))}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto",children:[(0,t.jsx)("h3",{className:"text-2xl font-bold text-[var(--color-black)] mb-4",children:"Not sure which category applies to you?"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"Our counseling guide helps you understand which entrance exams and counseling processes are relevant based on your location and preferences."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)(n.A,{variant:"primary",size:"md",onClick:()=>e.push("/guide"),children:"Counseling Guide"}),(0,t.jsx)(n.A,{variant:"outline",size:"md",onClick:()=>e.push("/colleges"),children:"Browse All Colleges"})]})]})})]})}},8801:(e,s,l)=>{"use strict";l.d(s,{default:()=>n});var t=l(5155),r=l(2115),a=l(5695),c=l(3741);let n=()=>{let[e,s]=(0,r.useState)(""),l=(0,a.useRouter)();return(0,t.jsxs)("section",{className:"min-h-screen flex items-center relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"w-full flex flex-col lg:flex-row min-h-screen",children:[(0,t.jsx)("div",{className:"w-full lg:w-1/2 bg-white flex items-center justify-center px-6 py-12 lg:px-16 lg:py-24",children:(0,t.jsxs)("div",{className:"max-w-2xl w-full text-center lg:text-left",children:[(0,t.jsxs)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-black)] leading-tight mb-8",children:["Find Your Perfect"," ",(0,t.jsx)("span",{className:"text-[var(--color-accent-brown)]",children:"Engineering College"})]}),(0,t.jsx)("p",{className:"text-lg md:text-xl text-gray-600 leading-relaxed mb-12 max-w-xl mx-auto lg:mx-0",children:"Discover detailed information about engineering colleges across India. Compare academics, hostels, fests, clubs, and placement statistics to make the best choice for your future."}),(0,t.jsx)("form",{onSubmit:s=>{s.preventDefault(),e.trim()&&l.push("/colleges?search=".concat(encodeURIComponent(e.trim())))},className:"mb-8",children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 max-w-lg mx-auto lg:mx-0",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("input",{type:"text",placeholder:"Search colleges, courses, or locations...",value:e,onChange:e=>s(e.target.value),className:"w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"})}),(0,t.jsx)(c.A,{type:"submit",variant:"primary",size:"md",className:"sm:px-8",children:"Search"})]})}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start",children:[(0,t.jsx)(c.A,{variant:"primary",size:"lg",onClick:()=>{l.push("/colleges")},className:"hover-button-scale",children:"Explore Colleges"}),(0,t.jsx)(c.A,{variant:"secondary",size:"lg",onClick:()=>l.push("/compare"),className:"hover-button-scale",children:"Compare Colleges"})]}),(0,t.jsxs)("div",{className:"mt-16 grid grid-cols-3 gap-8 text-center lg:text-left",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-[var(--color-accent-brown)]",children:"1000+"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Engineering Colleges"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-[var(--color-accent-brown)]",children:"50K+"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Student Reviews"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-2xl md:text-3xl font-bold text-[var(--color-accent-brown)]",children:"5 States"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 mt-1",children:"Counseling Coverage"})]})]})]})}),(0,t.jsxs)("div",{className:"w-full lg:w-1/2 relative min-h-[400px] lg:min-h-screen",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[var(--color-accent-brown)] to-[var(--color-accent-warm-brown)] z-10"}),(0,t.jsx)("div",{className:"absolute inset-0 z-20 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center text-white p-8",children:[(0,t.jsx)("div",{className:"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-6",children:(0,t.jsx)("svg",{className:"w-10 h-10",fill:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"})})}),(0,t.jsx)("h3",{className:"text-2xl font-semibold mb-3",children:"Trusted by Students"}),(0,t.jsx)("p",{className:"text-lg opacity-90",children:"Making informed college decisions easier"}),(0,t.jsxs)("div",{className:"mt-6 flex justify-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"1000+"}),(0,t.jsx)("div",{className:"text-sm opacity-80",children:"Colleges"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:"50K+"}),(0,t.jsx)("div",{className:"text-sm opacity-80",children:"Reviews"})]})]})]})})]})]}),(0,t.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 hidden lg:block",children:(0,t.jsx)("div",{className:"animate-bounce",children:(0,t.jsx)("svg",{className:"w-6 h-6 text-[var(--color-accent-brown)]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]})}}},e=>{e.O(0,[927,441,964,358],()=>e(e.s=3081)),_N_E=e.O()}]);