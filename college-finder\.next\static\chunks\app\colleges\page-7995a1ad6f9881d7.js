(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[569],{2686:(e,a,s)=>{Promise.resolve().then(s.bind(s,8381))},5695:(e,a,s)=>{"use strict";var n=s(8999);s.o(n,"useParams")&&s.d(a,{useParams:function(){return n.useParams}}),s.o(n,"usePathname")&&s.d(a,{usePathname:function(){return n.usePathname}}),s.o(n,"useRouter")&&s.d(a,{useRouter:function(){return n.useRouter}}),s.o(n,"useSearchParams")&&s.d(a,{useSearchParams:function(){return n.useSearchParams}})},8381:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>x});var n=s(5155),r=s(2115),l=s(5695),t=s(1514),i=s(7703),c=s(3741),o=s(9671),d=s(4028);let u=e=>{var a,s,l,t,o,u,m,x,g,h,v,p;let{filters:f,onFiltersChange:b,onClearFilters:j}=e,[y,N]=(0,r.useState)(!1),k=(e,a)=>{let s=a?parseInt(a):void 0;b({...f,nirfRanking:{...f.nirfRanking,[e]:s}})},w=(e,a)=>{let s=a?parseFloat(a):void 0;b({...f,rating:{...f.rating,[e]:s}})},C=!!((null==(a=f.counselingCategory)?void 0:a.length)||(null==(s=f.type)?void 0:s.length)||(null==(t=f.location)||null==(l=t.states)?void 0:l.length)||(null==(o=f.nirfRanking)?void 0:o.min)||(null==(u=f.nirfRanking)?void 0:u.max)||(null==(m=f.rating)?void 0:m.min)||(null==(x=f.rating)?void 0:x.max));return(0,n.jsxs)(i.Zp,{className:"sticky top-24",children:[(0,n.jsx)(i.aR,{children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)(i.ZB,{children:"Filters"}),(0,n.jsxs)("div",{className:"flex gap-2",children:[C&&(0,n.jsx)(c.A,{variant:"outline",size:"sm",onClick:j,children:"Clear All"}),(0,n.jsx)(c.A,{variant:"ghost",size:"sm",onClick:()=>N(!y),className:"md:hidden",children:y?"Hide":"Show"})]})]})}),(0,n.jsxs)(i.Wu,{className:"space-y-6 ".concat(y?"":"hidden md:block"),children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-3",children:"Counseling Category"}),(0,n.jsx)("div",{className:"space-y-2",children:d.K.map(e=>{var a;return(0,n.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:(null==(a=f.counselingCategory)?void 0:a.includes(e.name))||!1,onChange:a=>((e,a)=>{let s=f.counselingCategory||[],n=a?[...s,e]:s.filter(a=>a!==e);b({...f,counselingCategory:n.length>0?n:void 0})})(e.name,a.target.checked),className:"rounded border-gray-300 text-[var(--color-accent-brown)] focus:ring-[var(--color-accent-brown)]"}),(0,n.jsx)("span",{className:"text-sm",children:e.name}),(0,n.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.collegeCount,")"]})]},e.name)})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-3",children:"College Type"}),(0,n.jsx)("div",{className:"space-y-2",children:["Government","Private","Deemed"].map(e=>{var a;return(0,n.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:(null==(a=f.type)?void 0:a.includes(e))||!1,onChange:a=>((e,a)=>{let s=f.type||[],n=a?[...s,e]:s.filter(a=>a!==e);b({...f,type:n.length>0?n:void 0})})(e,a.target.checked),className:"rounded border-gray-300 text-[var(--color-accent-brown)] focus:ring-[var(--color-accent-brown)]"}),(0,n.jsx)("span",{className:"text-sm",children:e})]},e)})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-3",children:"State"}),(0,n.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:["Delhi","Karnataka","Maharashtra","West Bengal","Tamil Nadu","Uttar Pradesh","Gujarat","Rajasthan","Andhra Pradesh","Telangana"].map(e=>{var a,s;return(0,n.jsxs)("label",{className:"flex items-center gap-2 cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:(null==(s=f.location)||null==(a=s.states)?void 0:a.includes(e))||!1,onChange:a=>((e,a)=>{var s;let n=(null==(s=f.location)?void 0:s.states)||[],r=a?[...n,e]:n.filter(a=>a!==e);b({...f,location:{...f.location,states:r.length>0?r:void 0}})})(e,a.target.checked),className:"rounded border-gray-300 text-[var(--color-accent-brown)] focus:ring-[var(--color-accent-brown)]"}),(0,n.jsx)("span",{className:"text-sm",children:e})]},e)})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-3",children:"NIRF Ranking"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Min Rank"}),(0,n.jsx)("input",{type:"number",placeholder:"1",min:"1",max:"200",value:(null==(g=f.nirfRanking)?void 0:g.min)||"",onChange:e=>k("min",e.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Max Rank"}),(0,n.jsx)("input",{type:"number",placeholder:"200",min:"1",max:"200",value:(null==(h=f.nirfRanking)?void 0:h.max)||"",onChange:e=>k("max",e.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-3",children:"Rating"}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Min Rating"}),(0,n.jsx)("input",{type:"number",placeholder:"1.0",min:"1",max:"5",step:"0.1",value:(null==(v=f.rating)?void 0:v.min)||"",onChange:e=>w("min",e.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs text-gray-600 mb-1",children:"Max Rating"}),(0,n.jsx)("input",{type:"number",placeholder:"5.0",min:"1",max:"5",step:"0.1",value:(null==(p=f.rating)?void 0:p.max)||"",onChange:e=>w("max",e.target.value),className:"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold mb-3",children:"Quick Filters"}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(c.A,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>b({...f,nirfRanking:{min:1,max:50}}),children:"Top 50 NIRF Ranked"}),(0,n.jsx)(c.A,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>b({...f,rating:{min:4,max:5}}),children:"Highly Rated (4.0+)"}),(0,n.jsx)(c.A,{variant:"outline",size:"sm",className:"w-full justify-start",onClick:()=>b({...f,type:["Government"]}),children:"Government Colleges"})]})]})]})]})};var m=s(1380);let x=()=>{let e=(0,l.useSearchParams)().get("search")||"",[a,s]=(0,r.useState)(e),[x,g]=(0,r.useState)({query:e}),[h,v]=(0,r.useState)({field:"nirfRanking",direction:"asc"}),p=(0,r.useMemo)(()=>{var e;let a=d.$;if(x.query){let e=x.query.toLowerCase();a=a.filter(a=>a.name.toLowerCase().includes(e)||a.location.city.toLowerCase().includes(e)||a.location.state.toLowerCase().includes(e)||a.counselingCategory.toLowerCase().includes(e))}return x.counselingCategory&&x.counselingCategory.length>0&&(a=a.filter(e=>x.counselingCategory.includes(e.counselingCategory))),x.type&&x.type.length>0&&(a=a.filter(e=>x.type.includes(e.type))),(null==(e=x.location)?void 0:e.states)&&x.location.states.length>0&&(a=a.filter(e=>x.location.states.includes(e.location.state))),x.nirfRanking&&(a=a.filter(e=>{if(!e.nirfRanking)return!1;let{min:a,max:s}=x.nirfRanking;return(!a||e.nirfRanking>=a)&&(!s||e.nirfRanking<=s)})),x.rating&&(a=a.filter(e=>{let{min:a,max:s}=x.rating;return(!a||e.rating.overall>=a)&&(!s||e.rating.overall<=s)})),a.sort((e,a)=>{let s,n;switch(h.field){case"name":s=e.name,n=a.name;break;case"nirfRanking":s=e.nirfRanking||999,n=a.nirfRanking||999;break;case"rating":s=e.rating.overall,n=a.rating.overall;break;case"establishedYear":s=e.establishedYear,n=a.establishedYear;break;default:return 0}return"asc"===h.direction?s<n?-1:+(s>n):s>n?-1:+(s<n)}),a},[x,h]);return(0,n.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,n.jsxs)(t.wn,{background:"light",padding:"lg",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)(t._x,{size:"lg",className:"mb-4",children:"Find Your Perfect Engineering College"}),(0,n.jsxs)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:["Explore ",d.$.length," engineering colleges across India with detailed information about academics, placements, and campus life."]})]}),(0,n.jsx)("form",{onSubmit:e=>{e.preventDefault(),g(e=>({...e,query:a}))},className:"mb-8",children:(0,n.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 max-w-4xl mx-auto",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("input",{type:"text",placeholder:"Search colleges, locations, or counseling categories...",value:a,onChange:e=>s(e.target.value),className:"w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"})}),(0,n.jsx)(c.A,{type:"submit",variant:"primary",size:"md",children:"Search"})]})}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-4 justify-center mb-8",children:[(0,n.jsx)(c.A,{variant:x.counselingCategory?"outline":"primary",size:"sm",onClick:()=>g(e=>({...e,counselingCategory:void 0})),children:"All Categories"}),["JOSAA/CSAB","JAC Delhi","COMEDK/KCET","MHTCET","WBJEE"].map(e=>{var a;return(0,n.jsx)(c.A,{variant:(null==(a=x.counselingCategory)?void 0:a.includes(e))?"primary":"outline",size:"sm",onClick:()=>{let a=x.counselingCategory||[],s=a.includes(e)?a.filter(a=>a!==e):[...a,e];g(e=>({...e,counselingCategory:s.length>0?s:void 0}))},children:e},e)})]}),(0,n.jsx)("div",{className:"flex flex-wrap gap-4 justify-center",children:(0,n.jsxs)("select",{value:"".concat(h.field,"-").concat(h.direction),onChange:e=>{let[a,s]=e.target.value.split("-");v({field:a,direction:s})},className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)]",children:[(0,n.jsx)("option",{value:"nirfRanking-asc",children:"NIRF Ranking (Best First)"}),(0,n.jsx)("option",{value:"rating-desc",children:"Rating (Highest First)"}),(0,n.jsx)("option",{value:"name-asc",children:"Name (A-Z)"}),(0,n.jsx)("option",{value:"establishedYear-desc",children:"Newest First"}),(0,n.jsx)("option",{value:"establishedYear-asc",children:"Oldest First"})]})})]}),(0,n.jsx)(t.wn,{background:"white",padding:"lg",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,n.jsx)("div",{className:"lg:col-span-1",children:(0,n.jsx)(u,{filters:x,onFiltersChange:g,onClearFilters:()=>{g({query:a}),s("")}})}),(0,n.jsxs)("div",{className:"lg:col-span-3",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold text-[var(--color-black)] mb-2",children:[p.length," Colleges Found"]}),x.query&&(0,n.jsxs)("p",{className:"text-gray-600",children:['Showing results for "',x.query,'"']})]}),(0,n.jsx)(t.xA,{columns:2,gap:"lg",children:p.map(e=>(0,n.jsxs)(i.Zp,{hover:!0,padding:"none",className:"group cursor-pointer",onClick:()=>{var a;return a=e.id,void window.open("/college/".concat(a),"_blank")},children:[(0,n.jsx)(o.A,{text:"".concat(e.name," Campus"),aspectRatio:"16/9",color:"brown",className:"group-hover:scale-105"}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)(i.aR,{children:(0,n.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(i.ZB,{className:"group-hover:text-[var(--color-accent-brown)] transition-default text-lg leading-tight",children:e.name}),(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,n.jsxs)("span",{className:"text-gray-600 text-sm",children:[e.location.city,", ",e.location.state]}),(0,n.jsx)("span",{className:"text-gray-400",children:"•"}),(0,n.jsx)("span",{className:"text-[var(--color-accent-brown)] text-sm font-medium",children:e.type})]})]}),e.nirfRanking&&(0,n.jsxs)("div",{className:"bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold",children:["#",e.nirfRanking]})]})}),(0,n.jsxs)(i.Wu,{children:[(0,n.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-4",children:e.description}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,n.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"text-[var(--color-accent-brown)] font-bold text-lg",children:[e.academics.placementStats.placementPercentage,"%"]}),(0,n.jsx)("div",{className:"text-gray-500 text-xs",children:"Placement"})]}),(0,n.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[(0,n.jsx)("div",{className:"text-[var(--color-accent-brown)] font-bold text-lg",children:(0,m.vv)(e.academics.placementStats.averagePackage)}),(0,n.jsx)("div",{className:"text-gray-500 text-xs",children:"Avg Package"})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{className:"text-yellow-500 text-lg",children:(0,m.XK)(e.rating.overall)}),(0,n.jsx)("span",{className:"text-gray-700 font-medium",children:e.rating.overall})]}),(0,n.jsxs)("span",{className:"text-gray-500 text-sm",children:[e.reviews.length," reviews"]})]}),(0,n.jsx)("div",{className:"mb-4",children:(0,n.jsx)("span",{className:"inline-block bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] px-3 py-1 rounded-full text-sm font-medium",children:e.counselingCategory})}),(0,n.jsx)(c.A,{variant:"secondary",size:"sm",className:"w-full group-hover:bg-[var(--color-accent-brown)] group-hover:text-white group-hover:border-[var(--color-accent-brown)]",children:"View Details"})]})]})]},e.id))}),0===p.length&&(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,n.jsx)("h3",{className:"text-2xl font-bold text-gray-700 mb-2",children:"No colleges found"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"Try adjusting your search criteria or filters to find more results."}),(0,n.jsx)(c.A,{variant:"primary",onClick:()=>{s(""),g({})},children:"Clear All Filters"})]})]})]})})]})}}},e=>{e.O(0,[927,441,964,358],()=>e(e.s=2686)),_N_E=e.O()}]);