(()=>{var a={};a.id=402,a.ids=[402],a.modules={259:(a,b,c)=>{Promise.resolve().then(c.bind(c,4894))},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3819:(a,b,c)=>{Promise.resolve().then(c.bind(c,9332))},3856:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["compare",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,9332)),"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\compare\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\compare\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/compare/page",pathname:"/compare",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/compare/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3873:a=>{"use strict";a.exports=require("path")},4894:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m});var d=c(687),e=c(3210),f=c.n(e),g=c(8078),h=c(8749),i=c(2643),j=c(4665),k=c(2182),l=c(2342);let m=()=>{let[a,b]=(0,e.useState)([]),[c,m]=(0,e.useState)(""),n=k.$.filter(a=>a.name.toLowerCase().includes(c.toLowerCase())||a.location.city.toLowerCase().includes(c.toLowerCase())||a.location.state.toLowerCase().includes(c.toLowerCase())),o=c=>{b(a.filter(a=>a.id!==c))},p=[{category:"Basic Information",items:[{label:"Established Year",getValue:a=>a.establishedYear},{label:"Type",getValue:a=>a.type},{label:"Location",getValue:a=>`${a.location.city}, ${a.location.state}`},{label:"NIRF Ranking",getValue:a=>a.nirfRanking?`#${a.nirfRanking}`:"Not Ranked"},{label:"Counseling Category",getValue:a=>a.counselingCategory}]},{category:"Academics & Placements",items:[{label:"Departments",getValue:a=>a.academics.departments.length},{label:"Placement Rate",getValue:a=>`${a.academics.placementStats.placementPercentage}%`},{label:"Average Package",getValue:a=>(0,l.vv)(a.academics.placementStats.averagePackage)},{label:"Highest Package",getValue:a=>(0,l.vv)(a.academics.placementStats.highestPackage)},{label:"Research Papers",getValue:a=>a.academics.researchPapers}]},{category:"Ratings",items:[{label:"Overall Rating",getValue:a=>`${a.rating.overall} ${(0,l.XK)(a.rating.overall)}`},{label:"Academic Rating",getValue:a=>`${a.rating.academics} ${(0,l.XK)(a.rating.academics)}`},{label:"Infrastructure Rating",getValue:a=>`${a.rating.infrastructure} ${(0,l.XK)(a.rating.infrastructure)}`},{label:"Placement Rating",getValue:a=>`${a.rating.placements} ${(0,l.XK)(a.rating.placements)}`},{label:"Culture Rating",getValue:a=>`${a.rating.culture} ${(0,l.XK)(a.rating.culture)}`}]},{category:"Campus Life",items:[{label:"Hostels",getValue:a=>a.hostels.length},{label:"Festivals",getValue:a=>a.fests.length},{label:"Clubs",getValue:a=>a.clubs.length},{label:"Reviews",getValue:a=>a.reviews.length}]}];return(0,d.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,d.jsxs)(g.wn,{background:"light",padding:"lg",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)(g._x,{size:"lg",className:"mb-4",children:"Compare Engineering Colleges"}),(0,d.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Compare up to 3 colleges side-by-side to make an informed decision about your engineering education."})]}),a.length>0&&(0,d.jsxs)("div",{className:"mb-8",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold mb-4",children:["Selected Colleges (",a.length,"/3)"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-4",children:a.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-2 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] px-4 py-2 rounded-full",children:[(0,d.jsx)("span",{className:"font-medium",children:a.name}),(0,d.jsx)("button",{onClick:()=>o(a.id),className:"hover:bg-[var(--color-accent-brown)]/20 rounded-full p-1",children:(0,d.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})]},a.id))})]}),(0,d.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,d.jsx)("input",{type:"text",placeholder:"Search colleges to add for comparison...",value:c,onChange:a=>m(a.target.value),className:"w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"})})]}),c&&(0,d.jsxs)(g.wn,{background:"white",padding:"md",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Search Results"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:n.slice(0,6).map(c=>{let e=a.find(a=>a.id===c.id),f=a.length<3&&!e;return(0,d.jsx)(h.Zp,{className:e?"border-[var(--color-accent-brown)] bg-[var(--color-accent-brown)]/5":"",children:(0,d.jsxs)(h.Wu,{children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,d.jsx)(j.A,{text:c.name.substring(0,2),width:50,height:50,className:"rounded-lg",color:"brown"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"font-semibold text-sm",children:c.name}),(0,d.jsxs)("p",{className:"text-xs text-gray-600",children:[c.location.city,", ",c.location.state]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)("span",{className:"text-yellow-500 text-sm",children:(0,l.XK)(c.rating.overall)}),(0,d.jsx)("span",{className:"text-sm font-medium",children:c.rating.overall})]}),e?(0,d.jsx)(i.A,{variant:"outline",size:"sm",onClick:()=>o(c.id),children:"Remove"}):(0,d.jsx)(i.A,{variant:f?"primary":"outline",size:"sm",disabled:!f,onClick:()=>(c=>{a.length<3&&!a.find(a=>a.id===c.id)&&b([...a,c])})(c),children:f?"Add":"Max 3"})]})]})},c.id)})})]}),a.length>=2&&(0,d.jsxs)(g.wn,{background:"white",padding:"lg",children:[(0,d.jsx)(g._x,{size:"md",className:"mb-8",children:"College Comparison"}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full border-collapse",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"text-left p-4 border-b-2 border-gray-200 w-64",children:(0,d.jsx)("div",{className:"font-semibold text-gray-700",children:"Comparison Criteria"})}),a.map(a=>(0,d.jsx)("th",{className:"text-center p-4 border-b-2 border-gray-200 min-w-64",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(j.A,{text:a.name,aspectRatio:"16/9",color:"brown",className:"rounded-lg"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold text-[var(--color-accent-brown)]",children:a.name}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[a.location.city,", ",a.location.state]})]})]})},a.id))]})}),(0,d.jsx)("tbody",{children:p.map(b=>(0,d.jsxs)(f().Fragment,{children:[(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:a.length+1,className:"p-4 bg-gray-50 border-b border-gray-200",children:(0,d.jsx)("div",{className:"font-semibold text-[var(--color-accent-brown)]",children:b.category})})}),b.items.map(b=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"p-4 border-b border-gray-100 font-medium text-gray-700",children:b.label}),a.map(a=>(0,d.jsx)("td",{className:"p-4 border-b border-gray-100 text-center",children:(0,d.jsx)("div",{className:"font-medium",children:b.getValue(a)})},a.id))]},b.label))]},b.category))})]})}),(0,d.jsxs)("div",{className:"flex justify-center gap-4 mt-8",children:[(0,d.jsx)(i.A,{variant:"primary",size:"lg",children:"Download Comparison"}),(0,d.jsx)(i.A,{variant:"secondary",size:"lg",children:"Share Comparison"})]})]}),0===a.length&&!c&&(0,d.jsx)(g.wn,{background:"white",padding:"lg",children:(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("div",{className:"text-6xl mb-4",children:"⚖️"}),(0,d.jsx)("h3",{className:"text-2xl font-bold text-gray-700 mb-2",children:"Start Comparing Colleges"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6 max-w-md mx-auto",children:"Search and select up to 3 colleges to compare their features, ratings, and statistics side-by-side."}),(0,d.jsx)(i.A,{variant:"primary",onClick:()=>m("IIT"),children:"Search Colleges"})]})}),1===a.length&&(0,d.jsx)(g.wn,{background:"light",padding:"md",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"Add at least one more college to start comparing"}),(0,d.jsx)(i.A,{variant:"outline",onClick:()=>m(""),children:"Search More Colleges"})]})})]})}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9332:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\College Finder App\\\\college-finder\\\\src\\\\app\\\\compare\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\compare\\page.tsx","default")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,408,251,566],()=>b(b.s=3856));module.exports=c})();