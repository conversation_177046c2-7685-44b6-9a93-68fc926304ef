(()=>{var a={};a.id=28,a.ids=[28],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4406:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\College Finder App\\\\college-finder\\\\src\\\\app\\\\community\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\community\\page.tsx","default")},4424:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o});var d=c(687),e=c(3210),f=c(8078),g=c(8749),h=c(2643);let i=[{id:"q1",userId:"user-1",userName:"Rahul Kumar",title:"How is the placement scenario at IIT Delhi for CSE?",content:"I am considering IIT Delhi for Computer Science. Can someone share insights about the placement opportunities, average packages, and top recruiting companies?",tags:["IIT Delhi","CSE","Placements","Computer Science"],collegeId:"iit-delhi",answers:[{id:"a1",userId:"user-2",userName:"Priya Sharma",content:"IIT Delhi CSE has excellent placement records. The average package is around 18 LPA with top companies like Google, Microsoft, and Amazon regularly recruiting. The placement percentage is consistently above 95%.",votes:15,date:"2024-01-16",isAccepted:!0},{id:"a2",userId:"user-3",userName:"Amit Singh",content:"Apart from the high packages, IIT Delhi also provides great research opportunities. Many students opt for higher studies or start their own companies. The alumni network is very strong.",votes:8,date:"2024-01-17",isAccepted:!1}],votes:23,views:156,date:"2024-01-15",isResolved:!0},{id:"q2",userId:"user-4",userName:"Sneha Reddy",title:"Hostel facilities at DTU - Need honest reviews",content:"I got admission to DTU and need to know about hostel facilities. How are the rooms, mess food, and overall living conditions? Any specific hostel recommendations?",tags:["DTU","Hostels","Accommodation","Delhi"],collegeId:"dtu",answers:[{id:"a3",userId:"user-5",userName:"Vikash Gupta",content:"DTU hostels are decent but not luxurious. Boys hostels are better maintained than girls hostels. Mess food is average - you might want to explore nearby food options. Wi-Fi is good in most hostels.",votes:12,date:"2024-02-02",isAccepted:!0}],votes:18,views:89,date:"2024-02-01",isResolved:!0},{id:"q3",userId:"user-6",userName:"Arjun Patel",title:"RVCE vs PESIT - Which is better for CSE?",content:"I have offers from both RVCE and PESIT for Computer Science. Can someone help me compare these colleges in terms of academics, placements, and campus life?",tags:["RVCE","PESIT","CSE","Bangalore","Comparison"],collegeId:"rvce-bangalore",answers:[{id:"a4",userId:"user-7",userName:"Kavya Nair",content:"Both are good colleges. RVCE has a slight edge in terms of brand value and alumni network. PESIT has better infrastructure and modern facilities. For CSE, both offer similar placement opportunities.",votes:10,date:"2024-03-06",isAccepted:!1}],votes:15,views:67,date:"2024-03-05",isResolved:!1},{id:"q4",userId:"user-8",userName:"Ananya Das",title:"Research opportunities at Jadavpur University",content:"I am interested in pursuing research in Electronics. How are the research facilities and opportunities at JU? Are there good professors for guidance?",tags:["Jadavpur University","Research","Electronics","PhD"],collegeId:"jadavpur-university",answers:[{id:"a5",userId:"user-9",userName:"Souvik Das",content:"JU has excellent research facilities, especially in Electronics and Communication. Many professors are actively involved in research projects with ISRO and DRDO. The university encourages undergraduate research participation.",votes:20,date:"2024-03-01",isAccepted:!0}],votes:25,views:134,date:"2024-02-28",isResolved:!0}],j=[{id:"p1",userId:"user-10",userName:"College Finder Team",title:"Most Important Factor When Choosing a College",description:"What do you consider the most important factor when selecting an engineering college?",options:[{id:"o1",text:"Placement Records",votes:245},{id:"o2",text:"Academic Reputation",votes:189},{id:"o3",text:"Infrastructure & Facilities",votes:156},{id:"o4",text:"Location & Campus Life",votes:98},{id:"o5",text:"Fee Structure",votes:87}],totalVotes:775,expiryDate:"2024-12-31",tags:["College Selection","Survey","Engineering"],date:"2024-01-01"},{id:"p2",userId:"user-11",userName:"Tech Student",title:"Best Programming Language for Beginners",description:"Which programming language would you recommend for someone starting their coding journey in engineering?",options:[{id:"o6",text:"Python",votes:312},{id:"o7",text:"Java",votes:198},{id:"o8",text:"C++",votes:145},{id:"o9",text:"JavaScript",votes:89},{id:"o10",text:"C",votes:76}],totalVotes:820,tags:["Programming","Beginners","Technology"],date:"2024-02-15"},{id:"p3",userId:"user-12",userName:"Campus Life Explorer",title:"Most Exciting College Festival Type",description:"What type of college festival do you enjoy the most?",options:[{id:"o11",text:"Technical Fest (Robotics, Coding, etc.)",votes:167},{id:"o12",text:"Cultural Fest (Music, Dance, Drama)",votes:234},{id:"o13",text:"Sports Fest",votes:89},{id:"o14",text:"Literary Fest",votes:45}],totalVotes:535,collegeId:"general",tags:["Festivals","Campus Life","Events"],date:"2024-03-01"}];function k(a=5){return i.sort((a,b)=>b.votes-a.votes).slice(0,a)}function l(a=5){return i.sort((a,b)=>new Date(b.date).getTime()-new Date(a.date).getTime()).slice(0,a)}function m(){let a=new Date;return j.filter(b=>!b.expiryDate||new Date(b.expiryDate)>a)}var n=c(2342);let o=()=>{let[a,b]=(0,e.useState)("questions"),c=[{id:"questions",label:"All Questions",count:i.length},{id:"popular",label:"Popular",count:k().length},{id:"recent",label:"Recent",count:l().length},{id:"polls",label:"Polls",count:m().length}],j=(a,b)=>{console.log(`${b?"Upvoted":"Downvoted"} question ${a}`)};return(0,d.jsxs)("div",{className:"min-h-screen pt-20",children:[(0,d.jsx)(f.wn,{background:"dark",padding:"lg",children:(0,d.jsxs)("div",{className:"text-center text-white",children:[(0,d.jsx)(f._x,{size:"lg",className:"text-white mb-6",children:"College Community"}),(0,d.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8",children:"Connect with students, alumni, and experts. Ask questions, share experiences, and get insights about college life, academics, and career opportunities."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,d.jsx)(h.A,{variant:"primary",size:"lg",children:"Ask a Question"}),(0,d.jsx)(h.A,{variant:"secondary",size:"lg",className:"bg-transparent border-white text-white hover:bg-white hover:text-black",children:"Create Poll"})]})]})}),(0,d.jsx)(f.wn,{background:"light",padding:"sm",children:(0,d.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:c.map(c=>(0,d.jsxs)(h.A,{variant:a===c.id?"primary":"outline",size:"sm",onClick:()=>b(c.id),children:[c.label," (",c.count,")"]},c.id))})}),(0,d.jsx)(f.wn,{background:"white",padding:"lg",children:"polls"===a?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(f._x,{size:"md",children:"Active Polls"}),(0,d.jsx)("div",{className:"space-y-6",children:m().map(a=>(0,d.jsxs)(g.Zp,{children:[(0,d.jsx)(g.aR,{children:(0,d.jsxs)("div",{className:"flex items-start gap-4",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold",children:(0,n.IM)(a.userName)}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)(g.ZB,{className:"text-lg",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 mt-1",children:[(0,d.jsxs)("span",{children:["by ",a.userName]}),(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{children:(0,n.gs)(a.date)}),(0,d.jsx)("span",{children:"•"}),(0,d.jsxs)("span",{children:[a.totalVotes," votes"]})]})]})]})}),(0,d.jsxs)(g.Wu,{children:[(0,d.jsx)("p",{className:"text-gray-700 mb-6",children:a.description}),(0,d.jsx)("div",{className:"space-y-3",children:a.options.map(b=>{let c=a.totalVotes>0?b.votes/a.totalVotes*100:0;return(0,d.jsxs)("div",{className:"relative cursor-pointer group",onClick:()=>{var c,d;return c=a.id,d=b.id,void console.log(`Voted for option ${d} in poll ${c}`)},children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-[var(--color-accent-brown)] transition-default",children:[(0,d.jsx)("span",{className:"font-medium",children:b.text}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[b.votes," votes"]}),(0,d.jsxs)("span",{className:"text-sm font-bold text-[var(--color-accent-brown)]",children:[c.toFixed(1),"%"]})]})]}),(0,d.jsx)("div",{className:"absolute left-0 top-0 bottom-0 bg-[var(--color-accent-brown)]/10 rounded-lg transition-all duration-300",style:{width:`${c}%`}})]},b.id)})}),a.tags&&a.tags.length>0&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mt-4",children:a.tags.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full",children:a},b))})]})]},a.id))})]}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(f._x,{size:"md",children:"popular"===a?"Popular Questions":"recent"===a?"Recent Questions":"All Questions"}),(0,d.jsx)("div",{className:"space-y-6",children:(()=>{switch(a){case"popular":return k(10);case"recent":return l(10);default:return i}})().map(a=>(0,d.jsx)(g.Zp,{className:"hover:shadow-lg transition-default",children:(0,d.jsx)(g.Wu,{children:(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsxs)("div",{className:"flex flex-col items-center gap-2 min-w-[60px]",children:[(0,d.jsx)("button",{onClick:()=>j(a.id,!0),className:"p-2 rounded-full hover:bg-gray-100 transition-default",children:(0,d.jsx)("svg",{className:"w-5 h-5 text-gray-600 hover:text-[var(--color-accent-brown)]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"})})}),(0,d.jsx)("span",{className:"font-bold text-[var(--color-accent-brown)]",children:a.votes}),(0,d.jsx)("button",{onClick:()=>j(a.id,!1),className:"p-2 rounded-full hover:bg-gray-100 transition-default",children:(0,d.jsx)("svg",{className:"w-5 h-5 text-gray-600 hover:text-[var(--color-accent-brown)]",fill:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{d:"M12 20l1.41-1.41L7.83 13H20v-2H7.83l5.58-5.59L12 4l-8 8z"})})})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-start gap-4 mb-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 bg-[var(--color-accent-brown)] text-white rounded-full flex items-center justify-center font-bold",children:(0,n.IM)(a.userName)}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-[var(--color-black)] mb-2 hover:text-[var(--color-accent-brown)] cursor-pointer",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-3",children:[(0,d.jsxs)("span",{children:["by ",a.userName]}),(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{children:(0,n.gs)(a.date)}),(0,d.jsx)("span",{children:"•"}),(0,d.jsxs)("span",{children:[a.views," views"]}),a.isResolved&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Resolved"})]})]})]})]}),(0,d.jsx)("p",{className:"text-gray-700 mb-4 leading-relaxed",children:a.content}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:a.tags.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] text-sm rounded-full cursor-pointer hover:bg-[var(--color-accent-brown)]/20",children:a},b))}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,d.jsxs)("span",{children:[a.answers.length," answers"]}),a.answers.some(a=>a.isAccepted)&&(0,d.jsx)("span",{className:"text-green-600 font-medium",children:"✓ Has accepted answer"})]}),(0,d.jsx)(h.A,{variant:"outline",size:"sm",children:"View Details"})]}),a.answers.length>0&&(0,d.jsxs)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("div",{className:"w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold",children:(0,n.IM)(a.answers[0].userName)}),(0,d.jsx)("span",{className:"text-sm font-medium",children:a.answers[0].userName}),a.answers[0].isAccepted&&(0,d.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full",children:"✓ Accepted"}),(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:[a.answers[0].votes," votes"]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-700",children:a.answers[0].content.length>150?`${a.answers[0].content.substring(0,150)}...`:a.answers[0].content})]})]})]})})},a.id))})]})})]})}},4520:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["community",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,4406)),"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\community\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\College Finder App\\college-finder\\src\\app\\community\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/community/page",pathname:"/community",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/community/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},5593:(a,b,c)=>{Promise.resolve().then(c.bind(c,4424))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9145:(a,b,c)=>{Promise.resolve().then(c.bind(c,4406))},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,408,251],()=>b(b.s=4520));module.exports=c})();