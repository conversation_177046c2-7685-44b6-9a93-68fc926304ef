'use client';

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Section, SectionTitle, Grid } from '@/components/ui/Section';
import Button from '@/components/ui/Button';
import PlaceholderImage from '@/components/ui/PlaceholderImage';
import { mockColleges } from '@/data/colleges';
import { formatCurrency, generateStarRating, slugify } from '@/utils/cn';

const FeaturedColleges: React.FC = () => {
  const router = useRouter();
  const featuredColleges = mockColleges.slice(0, 3); // Show first 3 colleges as featured

  const handleCollegeClick = (collegeId: string) => {
    router.push(`/college/${collegeId}`);
  };

  const handleViewAll = () => {
    router.push('/colleges');
  };

  return (
    <Section background="dark" padding="xl" id="featured-colleges">
      <div className="text-center mb-16">
        <SectionTitle size="lg" className="text-white mb-6">
          Featured Engineering Colleges
        </SectionTitle>
        <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
          Discover top-rated engineering colleges with excellent academics, 
          infrastructure, and placement records.
        </p>
      </div>

      <Grid columns={3} gap="lg" className="mb-12">
        {featuredColleges.map((college) => (
          <Card
            key={college.id}
            hover
            padding="none"
            className="group cursor-pointer bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
            onClick={() => handleCollegeClick(college.id)}
          >
            <PlaceholderImage
              text={`${college.name} Campus`}
              aspectRatio="16/9"
              color="brown"
              className="group-hover:scale-105"
            />

            <div className="p-6">
              <CardHeader>
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <CardTitle className="text-white group-hover:text-[var(--color-accent-brown)] transition-default text-lg leading-tight">
                      {college.name}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-gray-300 text-sm">
                        {college.location.city}, {college.location.state}
                      </span>
                      <span className="text-gray-400">•</span>
                      <span className="text-[var(--color-accent-brown)] text-sm font-medium">
                        {college.type}
                      </span>
                    </div>
                  </div>
                  {college.nirfRanking && (
                    <div className="bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold">
                      #{college.nirfRanking}
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-gray-300 text-sm leading-relaxed mb-4">
                  {college.description}
                </p>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="text-center p-3 bg-white/5 rounded-lg">
                    <div className="text-[var(--color-accent-brown)] font-bold text-lg">
                      {college.academics.placementStats.placementPercentage}%
                    </div>
                    <div className="text-gray-400 text-xs">Placement</div>
                  </div>
                  <div className="text-center p-3 bg-white/5 rounded-lg">
                    <div className="text-[var(--color-accent-brown)] font-bold text-lg">
                      {formatCurrency(college.academics.placementStats.averagePackage)}
                    </div>
                    <div className="text-gray-400 text-xs">Avg Package</div>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-yellow-400 text-lg">
                      {generateStarRating(college.rating.overall)}
                    </span>
                    <span className="text-white font-medium">
                      {college.rating.overall}
                    </span>
                  </div>
                  <span className="text-gray-400 text-sm">
                    {college.reviews.length} reviews
                  </span>
                </div>

                <div className="mb-6">
                  <span className="inline-block bg-[var(--color-accent-brown)]/20 text-[var(--color-accent-brown)] px-3 py-1 rounded-full text-sm font-medium">
                    {college.counselingCategory}
                  </span>
                </div>

                <Button
                  variant="secondary"
                  size="sm"
                  className="w-full bg-transparent border-white/30 text-white hover:bg-[var(--color-accent-brown)] hover:border-[var(--color-accent-brown)]"
                >
                  View Details
                  <svg
                    className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </Button>
              </CardContent>
            </div>
          </Card>
        ))}
      </Grid>

      {/* View All Button */}
      <div className="text-center">
        <Button 
          variant="primary" 
          size="lg"
          onClick={handleViewAll}
          className="hover-button-scale"
        >
          View All Colleges
          <svg 
            className="w-5 h-5 ml-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M13 7l5 5m0 0l-5 5m5-5H6" 
            />
          </svg>
        </Button>
      </div>

      {/* Additional Features */}
      <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-[var(--color-accent-brown)]/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-[var(--color-accent-brown)]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Verified Reviews</h3>
          <p className="text-gray-300 text-sm">
            Read authentic reviews from current students and alumni
          </p>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 bg-[var(--color-accent-brown)]/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-[var(--color-accent-brown)]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H15V1h-2v1H9V1H7v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2z"/>
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Detailed Analytics</h3>
          <p className="text-gray-300 text-sm">
            Compare placement stats, fees, and academic performance
          </p>
        </div>
        
        <div className="text-center">
          <div className="w-16 h-16 bg-[var(--color-accent-brown)]/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-[var(--color-accent-brown)]" fill="currentColor" viewBox="0 0 24 24">
              <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z"/>
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-white mb-2">Easy Comparison</h3>
          <p className="text-gray-300 text-sm">
            Side-by-side comparison of multiple colleges
          </p>
        </div>
      </div>
    </Section>
  );
};

export default FeaturedColleges;
