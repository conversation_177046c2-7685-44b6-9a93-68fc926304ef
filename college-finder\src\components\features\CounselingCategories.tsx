'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Section, SectionTitle, Grid } from '@/components/ui/Section';
import Button from '@/components/ui/Button';
import { counselingCategories } from '@/data/colleges';
import { slugify } from '@/utils/cn';

const CounselingCategories: React.FC = () => {
  const router = useRouter();

  const categoryIcons = {
    'JOSAA/CSAB': (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
      </svg>
    ),
    'JAC Delhi': (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>
    ),
    'COMEDK/KCET': (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
      </svg>
    ),
    'MHTCET': (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
      </svg>
    ),
    'WBJEE': (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H15V1h-2v1H9V1H7v1H4.5C3.67 2 3 2.67 3 3.5v15C3 19.33 3.67 20 4.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C21 2.67 20.33 2 19.5 2z"/>
      </svg>
    )
  };

  const handleCategoryClick = (categoryName: string) => {
    const slug = slugify(categoryName);
    router.push(`/counseling/${slug}`);
  };

  return (
    <Section background="light" padding="xl" id="counseling-categories">
      <div className="text-center mb-16">
        <SectionTitle size="lg" className="mb-6">
          Choose Your Counseling Category
        </SectionTitle>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          Find colleges based on your entrance exam and counseling process. 
          Each category covers specific states and examination boards.
        </p>
      </div>

      <Grid columns={3} gap="lg" className="mb-12">
        {counselingCategories.map((category) => (
          <Card
            key={category.name}
            hover
            className="group cursor-pointer border-2 border-transparent hover:border-[var(--color-accent-brown)]/20"
            onClick={() => handleCategoryClick(category.name)}
          >
            <CardHeader>
              <div className="flex items-center justify-between mb-4">
                <div className="w-16 h-16 bg-[var(--color-accent-brown)]/10 rounded-full flex items-center justify-center text-[var(--color-accent-brown)] group-hover:bg-[var(--color-accent-brown)] group-hover:text-white transition-default">
                  {categoryIcons[category.name as keyof typeof categoryIcons]}
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-[var(--color-accent-brown)]">
                    {category.collegeCount}
                  </div>
                  <div className="text-sm text-gray-500">Colleges</div>
                </div>
              </div>
              <CardTitle className="group-hover:text-[var(--color-accent-brown)] transition-default">
                {category.name}
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <p className="text-gray-600 mb-4 leading-relaxed">
                {category.description}
              </p>
              
              <div className="mb-6">
                <div className="text-sm font-medium text-gray-700 mb-2">Coverage:</div>
                <div className="flex flex-wrap gap-2">
                  {category.states.map((state) => (
                    <span
                      key={state}
                      className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                    >
                      {state}
                    </span>
                  ))}
                </div>
              </div>

              <Button
                variant="secondary"
                size="sm"
                className="w-full group-hover:bg-[var(--color-accent-brown)] group-hover:text-white group-hover:border-[var(--color-accent-brown)]"
              >
                Explore Colleges
                <svg 
                  className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 5l7 7-7 7" 
                  />
                </svg>
              </Button>
            </CardContent>
          </Card>
        ))}
      </Grid>

      {/* Additional Information */}
      <div className="text-center">
        <div className="bg-white rounded-2xl p-8 shadow-lg max-w-4xl mx-auto">
          <h3 className="text-2xl font-bold text-[var(--color-black)] mb-4">
            Not sure which category applies to you?
          </h3>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Our counseling guide helps you understand which entrance exams and counseling processes 
            are relevant based on your location and preferences.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              variant="primary" 
              size="md"
              onClick={() => router.push('/guide')}
            >
              Counseling Guide
            </Button>
            <Button 
              variant="outline" 
              size="md"
              onClick={() => router.push('/colleges')}
            >
              Browse All Colleges
            </Button>
          </div>
        </div>
      </div>
    </Section>
  );
};

export default CounselingCategories;
