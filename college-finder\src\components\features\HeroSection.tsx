'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';

const HeroSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/colleges?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleGetStarted = () => {
    router.push('/colleges');
  };

  return (
    <section className="min-h-screen flex items-center relative overflow-hidden">
      {/* Split Layout Container */}
      <div className="w-full flex flex-col lg:flex-row min-h-screen">
        {/* Left Panel - Content */}
        <div className="w-full lg:w-1/2 bg-white flex items-center justify-center px-6 py-12 lg:px-16 lg:py-24">
          <div className="max-w-2xl w-full text-center lg:text-left">
            {/* Hero Title */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-[var(--color-black)] leading-tight mb-8">
              Find Your Perfect{' '}
              <span className="text-[var(--color-accent-brown)]">
                Engineering College
              </span>
            </h1>

            {/* Hero Subtitle */}
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed mb-12 max-w-xl mx-auto lg:mx-0">
              Discover detailed information about engineering colleges across India. 
              Compare academics, hostels, fests, clubs, and placement statistics to make the best choice for your future.
            </p>

            {/* Search Bar */}
            <form onSubmit={handleSearch} className="mb-8">
              <div className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto lg:mx-0">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search colleges, courses, or locations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"
                  />
                </div>
                <Button 
                  type="submit" 
                  variant="primary" 
                  size="md"
                  className="sm:px-8"
                >
                  Search
                </Button>
              </div>
            </form>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button 
                variant="primary" 
                size="lg"
                onClick={handleGetStarted}
                className="hover-button-scale"
              >
                Explore Colleges
              </Button>
              <Button 
                variant="secondary" 
                size="lg"
                onClick={() => router.push('/compare')}
                className="hover-button-scale"
              >
                Compare Colleges
              </Button>
            </div>

            {/* Quick Stats */}
            <div className="mt-16 grid grid-cols-3 gap-8 text-center lg:text-left">
              <div>
                <div className="text-2xl md:text-3xl font-bold text-[var(--color-accent-brown)]">
                  1000+
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Engineering Colleges
                </div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-[var(--color-accent-brown)]">
                  50K+
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Student Reviews
                </div>
              </div>
              <div>
                <div className="text-2xl md:text-3xl font-bold text-[var(--color-accent-brown)]">
                  5 States
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Counseling Coverage
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Hero Image */}
        <div className="w-full lg:w-1/2 relative min-h-[400px] lg:min-h-screen">
          <div className="absolute inset-0 bg-gradient-to-br from-[var(--color-accent-brown)] to-[var(--color-accent-warm-brown)] z-10" />

          {/* Floating Elements */}
          <div className="absolute inset-0 z-20 flex items-center justify-center">
            <div className="text-center text-white p-8">
              <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-3">Trusted by Students</h3>
              <p className="text-lg opacity-90">Making informed college decisions easier</p>
              <div className="mt-6 flex justify-center space-x-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">1000+</div>
                  <div className="text-sm opacity-80">Colleges</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">50K+</div>
                  <div className="text-sm opacity-80">Reviews</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 hidden lg:block">
        <div className="animate-bounce">
          <svg 
            className="w-6 h-6 text-[var(--color-accent-brown)]" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M19 14l-7 7m0 0l-7-7m7 7V3" 
            />
          </svg>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
