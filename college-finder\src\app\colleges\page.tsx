'use client';

import React, { useState, useMemo } from 'react';
import { Section, SectionTitle, Grid } from '@/components/ui/Section';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import PlaceholderImage from '@/components/ui/PlaceholderImage';
import SearchFilters from '@/components/features/SearchFilters';
import { mockColleges } from '@/data/colleges';
import { SearchFilters as SearchFiltersType, SortOption, CounselingCategory } from '@/types';
import { formatCurrency, generateStarRating } from '@/utils/cn';

const CollegesPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFiltersType>({});
  const [sortOption, setSortOption] = useState<SortOption>({
    field: 'nirfRanking',
    direction: 'asc'
  });

  // Filter and sort colleges
  const filteredAndSortedColleges = useMemo(() => {
    let filtered = mockColleges;

    // Apply search query
    if (filters.query) {
      const query = filters.query.toLowerCase();
      filtered = filtered.filter(college =>
        college.name.toLowerCase().includes(query) ||
        college.location.city.toLowerCase().includes(query) ||
        college.location.state.toLowerCase().includes(query) ||
        college.counselingCategory.toLowerCase().includes(query)
      );
    }

    // Apply counseling category filter
    if (filters.counselingCategory && filters.counselingCategory.length > 0) {
      filtered = filtered.filter(college =>
        filters.counselingCategory!.includes(college.counselingCategory)
      );
    }

    // Apply type filter
    if (filters.type && filters.type.length > 0) {
      filtered = filtered.filter(college =>
        filters.type!.includes(college.type)
      );
    }

    // Apply location filter
    if (filters.location?.states && filters.location.states.length > 0) {
      filtered = filtered.filter(college =>
        filters.location!.states!.includes(college.location.state)
      );
    }

    // Apply NIRF ranking filter
    if (filters.nirfRanking) {
      filtered = filtered.filter(college => {
        if (!college.nirfRanking) return false;
        const { min, max } = filters.nirfRanking!;
        return (!min || college.nirfRanking >= min) && (!max || college.nirfRanking <= max);
      });
    }

    // Apply rating filter
    if (filters.rating) {
      filtered = filtered.filter(college => {
        const { min, max } = filters.rating!;
        return (!min || college.rating.overall >= min) && (!max || college.rating.overall <= max);
      });
    }

    // Sort colleges
    filtered.sort((a, b) => {
      let aValue: string | number, bValue: string | number;
      
      switch (sortOption.field) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'nirfRanking':
          aValue = a.nirfRanking || 999;
          bValue = b.nirfRanking || 999;
          break;
        case 'rating':
          aValue = a.rating.overall;
          bValue = b.rating.overall;
          break;
        case 'establishedYear':
          aValue = a.establishedYear;
          bValue = b.establishedYear;
          break;
        default:
          return 0;
      }

      if (sortOption.direction === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [filters, sortOption]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({ ...prev, query: searchQuery }));
  };

  const handleCollegeClick = (collegeId: string) => {
    window.open(`/college/${collegeId}`, '_blank');
  };

  return (
    <div className="min-h-screen pt-20">
      {/* Search and Filters Section */}
      <Section background="light" padding="lg">
        <div className="text-center mb-8">
          <SectionTitle size="lg" className="mb-4">
            Find Your Perfect Engineering College
          </SectionTitle>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore {mockColleges.length} engineering colleges across India with detailed information about academics, placements, and campus life.
          </p>
        </div>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 max-w-4xl mx-auto">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search colleges, locations, or counseling categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"
              />
            </div>
            <Button type="submit" variant="primary" size="md">
              Search
            </Button>
          </div>
        </form>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-4 justify-center mb-8">
          <Button
            variant={!filters.counselingCategory ? "primary" : "outline"}
            size="sm"
            onClick={() => setFilters(prev => ({ ...prev, counselingCategory: undefined }))}
          >
            All Categories
          </Button>
          {['JOSAA/CSAB', 'JAC Delhi', 'COMEDK/KCET', 'MHTCET', 'WBJEE'].map(category => (
            <Button
              key={category}
              variant={filters.counselingCategory?.includes(category as CounselingCategory) ? "primary" : "outline"}
              size="sm"
              onClick={() => {
                const current = filters.counselingCategory || [];
                const categoryTyped = category as CounselingCategory;
                const updated = current.includes(categoryTyped)
                  ? current.filter(c => c !== category)
                  : [...current, categoryTyped];
                setFilters(prev => ({ ...prev, counselingCategory: updated.length > 0 ? updated : undefined }));
              }}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Sort Options */}
        <div className="flex flex-wrap gap-4 justify-center">
          <select
            value={`${sortOption.field}-${sortOption.direction}`}
            onChange={(e) => {
              const [field, direction] = e.target.value.split('-');
              setSortOption({
                field: field as SortOption['field'],
                direction: direction as SortOption['direction']
              });
            }}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)]"
          >
            <option value="nirfRanking-asc">NIRF Ranking (Best First)</option>
            <option value="rating-desc">Rating (Highest First)</option>
            <option value="name-asc">Name (A-Z)</option>
            <option value="establishedYear-desc">Newest First</option>
            <option value="establishedYear-asc">Oldest First</option>
          </select>
        </div>
      </Section>

      {/* Results Section */}
      <Section background="white" padding="lg">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <SearchFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClearFilters={() => {
                setFilters({ query: searchQuery });
                setSearchQuery('');
              }}
            />
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-[var(--color-black)] mb-2">
                {filteredAndSortedColleges.length} Colleges Found
              </h2>
              {filters.query && (
                <p className="text-gray-600">
                  Showing results for &quot;{filters.query}&quot;
                </p>
              )}
            </div>

            <Grid columns={2} gap="lg">
          {filteredAndSortedColleges.map((college) => (
            <Card
              key={college.id}
              hover
              padding="none"
              className="group cursor-pointer"
              onClick={() => handleCollegeClick(college.id)}
            >
              <PlaceholderImage
                text={`${college.name} Campus`}
                aspectRatio="16/9"
                color="brown"
                className="group-hover:scale-105"
              />
              
              <div className="p-6">
                <CardHeader>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <CardTitle className="group-hover:text-[var(--color-accent-brown)] transition-default text-lg leading-tight">
                        {college.name}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-gray-600 text-sm">
                          {college.location.city}, {college.location.state}
                        </span>
                        <span className="text-gray-400">•</span>
                        <span className="text-[var(--color-accent-brown)] text-sm font-medium">
                          {college.type}
                        </span>
                      </div>
                    </div>
                    {college.nirfRanking && (
                      <div className="bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold">
                        #{college.nirfRanking}
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {college.description}
                  </p>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-[var(--color-accent-brown)] font-bold text-lg">
                        {college.academics.placementStats.placementPercentage}%
                      </div>
                      <div className="text-gray-500 text-xs">Placement</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-[var(--color-accent-brown)] font-bold text-lg">
                        {formatCurrency(college.academics.placementStats.averagePackage)}
                      </div>
                      <div className="text-gray-500 text-xs">Avg Package</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-500 text-lg">
                        {generateStarRating(college.rating.overall)}
                      </span>
                      <span className="text-gray-700 font-medium">
                        {college.rating.overall}
                      </span>
                    </div>
                    <span className="text-gray-500 text-sm">
                      {college.reviews.length} reviews
                    </span>
                  </div>

                  <div className="mb-4">
                    <span className="inline-block bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] px-3 py-1 rounded-full text-sm font-medium">
                      {college.counselingCategory}
                    </span>
                  </div>

                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-full group-hover:bg-[var(--color-accent-brown)] group-hover:text-white group-hover:border-[var(--color-accent-brown)]"
                  >
                    View Details
                  </Button>
                </CardContent>
              </div>
            </Card>
          ))}
            </Grid>

            {filteredAndSortedColleges.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-bold text-gray-700 mb-2">No colleges found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search criteria or filters to find more results.
                </p>
                <Button
                  variant="primary"
                  onClick={() => {
                    setSearchQuery('');
                    setFilters({});
                  }}
                >
                  Clear All Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </Section>
    </div>
  );
};

  return (
    <div className="min-h-screen pt-20">
      {/* Search and Filters Section */}
      <Section background="light" padding="lg">
        <div className="text-center mb-8">
          <SectionTitle size="lg" className="mb-4">
            Find Your Perfect Engineering College
          </SectionTitle>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore {mockColleges.length} engineering colleges across India with detailed information about academics, placements, and campus life.
          </p>
        </div>

        {/* Search Bar */}
        <form onSubmit={handleSearch} className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 max-w-4xl mx-auto">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search colleges, locations, or counseling categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-6 py-4 text-base border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)] focus:border-transparent transition-default"
              />
            </div>
            <Button type="submit" variant="primary" size="md">
              Search
            </Button>
          </div>
        </form>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-4 justify-center mb-8">
          <Button
            variant={!filters.counselingCategory ? "primary" : "outline"}
            size="sm"
            onClick={() => setFilters(prev => ({ ...prev, counselingCategory: undefined }))}
          >
            All Categories
          </Button>
          {['JOSAA/CSAB', 'JAC Delhi', 'COMEDK/KCET', 'MHTCET', 'WBJEE'].map(category => (
            <Button
              key={category}
              variant={filters.counselingCategory?.includes(category as CounselingCategory) ? "primary" : "outline"}
              size="sm"
              onClick={() => {
                const current = filters.counselingCategory || [];
                const categoryTyped = category as CounselingCategory;
                const updated = current.includes(categoryTyped)
                  ? current.filter(c => c !== category)
                  : [...current, categoryTyped];
                setFilters(prev => ({ ...prev, counselingCategory: updated.length > 0 ? updated : undefined }));
              }}
            >
              {category}
            </Button>
          ))}
        </div>

        {/* Sort Options */}
        <div className="flex flex-wrap gap-4 justify-center">
          <select
            value={`${sortOption.field}-${sortOption.direction}`}
            onChange={(e) => {
              const [field, direction] = e.target.value.split('-');
              setSortOption({
                field: field as SortOption['field'],
                direction: direction as SortOption['direction']
              });
            }}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--color-accent-brown)]"
          >
            <option value="nirfRanking-asc">NIRF Ranking (Best First)</option>
            <option value="rating-desc">Rating (Highest First)</option>
            <option value="name-asc">Name (A-Z)</option>
            <option value="establishedYear-desc">Newest First</option>
            <option value="establishedYear-asc">Oldest First</option>
          </select>
        </div>
      </Section>

      {/* Results Section */}
      <Section background="white" padding="lg">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <SearchFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClearFilters={() => {
                setFilters({});
                setSearchQuery('');
              }}
            />
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-[var(--color-black)] mb-2">
                {filteredAndSortedColleges.length} Colleges Found
              </h2>
              {filters.query && (
                <p className="text-gray-600">
                  Showing results for &quot;{filters.query}&quot;
                </p>
              )}
            </div>

            <Grid columns={2} gap="lg">
          {filteredAndSortedColleges.map((college) => (
            <Card
              key={college.id}
              hover
              padding="none"
              className="group cursor-pointer"
              onClick={() => handleCollegeClick(college.id)}
            >
              <PlaceholderImage
                text={`${college.name} Campus`}
                aspectRatio="16/9"
                color="brown"
                className="group-hover:scale-105"
              />

              <div className="p-6">
                <CardHeader>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <CardTitle className="group-hover:text-[var(--color-accent-brown)] transition-default text-lg leading-tight">
                        {college.name}
                      </CardTitle>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-gray-600 text-sm">
                          {college.location.city}, {college.location.state}
                        </span>
                        <span className="text-gray-400">•</span>
                        <span className="text-[var(--color-accent-brown)] text-sm font-medium">
                          {college.type}
                        </span>
                      </div>
                    </div>
                    {college.nirfRanking && (
                      <div className="bg-[var(--color-accent-brown)] text-white px-3 py-1 rounded-full text-sm font-bold">
                        #{college.nirfRanking}
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent>
                  <p className="text-gray-600 text-sm leading-relaxed mb-4">
                    {college.description}
                  </p>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-[var(--color-accent-brown)] font-bold text-lg">
                        {college.academics.placementStats.placementPercentage}%
                      </div>
                      <div className="text-gray-500 text-xs">Placement</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-[var(--color-accent-brown)] font-bold text-lg">
                        {formatCurrency(college.academics.placementStats.averagePackage)}
                      </div>
                      <div className="text-gray-500 text-xs">Avg Package</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-500 text-lg">
                        {generateStarRating(college.rating.overall)}
                      </span>
                      <span className="text-gray-700 font-medium">
                        {college.rating.overall}
                      </span>
                    </div>
                    <span className="text-gray-500 text-sm">
                      {college.reviews.length} reviews
                    </span>
                  </div>

                  <div className="mb-4">
                    <span className="inline-block bg-[var(--color-accent-brown)]/10 text-[var(--color-accent-brown)] px-3 py-1 rounded-full text-sm font-medium">
                      {college.counselingCategory}
                    </span>
                  </div>

                  <Button
                    variant="secondary"
                    size="sm"
                    className="w-full group-hover:bg-[var(--color-accent-brown)] group-hover:text-white group-hover:border-[var(--color-accent-brown)]"
                  >
                    View Details
                  </Button>
                </CardContent>
              </div>
            </Card>
          ))}
            </Grid>

            {filteredAndSortedColleges.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-2xl font-bold text-gray-700 mb-2">No colleges found</h3>
                <p className="text-gray-600 mb-6">
                  Try adjusting your search criteria or filters to find more results.
                </p>
                <Button
                  variant="primary"
                  onClick={() => {
                    setSearchQuery('');
                    setFilters({});
                  }}
                >
                  Clear All Filters
                </Button>
              </div>
            )}
          </div>
        </div>
      </Section>
    </div>
  );
};

export default CollegesPage;
